/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: [
      'propbolt.com',
      'api.mapbox.com',
      'api.realestateapi.com'
    ],
  },
  env: {
    NEXT_PUBLIC_API_BASE_URL: 'https://propbolt.com',
    NEXT_PUBLIC_MAPBOX_TOKEN: 'pk.eyJ1Ijoic2F2ZXVuZGVyY29udHJhY3QiLCJhIjoiY202anNoNWtvMDRpMjJqb2x1MWZ3ZGYxbCJ9.Bxl18yPka0BUSrGibMTbhg',
    NEXT_PUBLIC_REAL_ESTATE_API_KEY: 'AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914',
    NEXT_PUBLIC_REAL_ESTATE_API_URL: 'https://api.realestateapi.com/v2/'
  },
  async rewrites() {
    // Environment-based API routing
    let apiBaseUrl;

    if (process.env.NODE_ENV === 'development') {
      // Development: Route to local backend
      apiBaseUrl = 'http://localhost:8080';
    } else if (process.env.NEXT_PUBLIC_LOCAL_PRODUCTION === 'true') {
      // Local production testing: Route to local frontend
      apiBaseUrl = 'http://localhost:3000';
    } else {
      // Production: Route to production domain
      apiBaseUrl = 'https://propbolt.com';
    }

    return [
      {
        source: '/api/:path*',
        destination: `${apiBaseUrl}/api/:path*`,
      },
    ];
  },
}

module.exports = nextConfig
