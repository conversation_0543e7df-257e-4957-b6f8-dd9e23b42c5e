# Production API Configuration
NEXT_PUBLIC_API_BASE_URL=https://propbolt.com

# Mapbox Configuration
NEXT_PUBLIC_MAPBOX_TOKEN=pk.eyJ1Ijoic2F2ZXVuZGVyY29udHJhY3QiLCJhIjoiY202anNoNWtvMDRpMjJqb2x1MWZ3ZGYxbCJ9.Bxl18yPka0BUSrGibMTbhg

# Real Estate API Configuration
NEXT_PUBLIC_REAL_ESTATE_API_KEY=AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914
NEXT_PUBLIC_REAL_ESTATE_API_URL=https://api.realestateapi.com/v2/

# Application Configuration
NEXT_PUBLIC_APP_NAME=Vacant Land Search
NEXT_PUBLIC_APP_DESCRIPTION=Professional real estate admin panel for vacant land search in Daytona Beach, Florida

# Environment Configuration
DEMO_MODE=false
NODE_ENV=production

# Database Configuration
DATABASE_URL=postgresql://wonderland_owner:<EMAIL>/wonderland?sslmode=require
