#!/bin/bash

# 🔍 Check Deployment Files Script
# Verifies file count for Google App Engine deployment without actually deploying

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_step() {
    echo -e "${BLUE}[CHECK]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo -e "${BLUE}🔍 Checking deployment file count...${NC}"
echo ""

# Check if Next.js is built
if [ ! -d ".next" ]; then
    print_error "Next.js not built. Run 'npm run build' first."
    exit 1
fi

# Use frontend-specific .gcloudignore
if [ ! -f ".gcloudignore-frontend" ]; then
    print_error ".gcloudignore-frontend not found"
    exit 1
fi

# Backup and use frontend .gcloudignore
if [ -f ".gcloudignore" ]; then
    cp .gcloudignore .gcloudignore.backup
fi
cp .gcloudignore-frontend .gcloudignore

print_step "Simulating gcloud deployment file selection..."

# Create temporary directory to simulate deployment
TEMP_DIR=$(mktemp -d)
rsync -av --exclude-from=.gcloudignore . "$TEMP_DIR/" > /dev/null 2>&1

# Count files
FILE_COUNT=$(find "$TEMP_DIR" -type f | wc -l)

# Show file breakdown
echo ""
print_step "File breakdown:"
echo "  .next/static files: $(find "$TEMP_DIR/.next/static" -type f 2>/dev/null | wc -l)"
echo "  .next/server files: $(find "$TEMP_DIR/.next/server" -type f 2>/dev/null | wc -l)"
echo "  src/ files: $(find "$TEMP_DIR/src" -type f 2>/dev/null | wc -l)"
echo "  public/ files: $(find "$TEMP_DIR/public" -type f 2>/dev/null | wc -l)"
echo "  Config files: $(find "$TEMP_DIR" -maxdepth 1 -type f | wc -l)"

echo ""
print_step "Total files to be deployed: $FILE_COUNT"

# Check against limits
if [ "$FILE_COUNT" -gt 10000 ]; then
    print_error "❌ File count ($FILE_COUNT) exceeds App Engine limit of 10,000 files"
    echo ""
    echo "Top directories by file count:"
    find "$TEMP_DIR" -type f | sed "s|$TEMP_DIR/||" | cut -d'/' -f1 | sort | uniq -c | sort -nr | head -10
    RESULT=1
elif [ "$FILE_COUNT" -gt 8000 ]; then
    print_warning "⚠️  File count ($FILE_COUNT) is high but within limits"
    RESULT=0
else
    print_success "✅ File count ($FILE_COUNT) is well within limits"
    RESULT=0
fi

# Cleanup
rm -rf "$TEMP_DIR"

# Restore original .gcloudignore
if [ -f ".gcloudignore.backup" ]; then
    mv .gcloudignore.backup .gcloudignore
fi

echo ""
if [ $RESULT -eq 0 ]; then
    print_success "✅ Deployment should succeed!"
else
    print_error "❌ Deployment will likely fail due to file count"
fi

exit $RESULT
