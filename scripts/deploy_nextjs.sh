#!/bin/bash

# 🚀 Next.js Deployment Script for Vacant Land Search Dashboard
# This script builds and deploys the Next.js frontend

set -e

echo "🏖️ Deploying Vacant Land Search Next.js Frontend..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18+ is required. Current version: $(node --version)"
    exit 1
fi

print_success "Node.js version check passed: $(node --version)"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

print_success "npm version: $(npm --version)"

# Install dependencies
print_status "Installing dependencies..."
npm ci --silent

# Run type checking
print_status "Running TypeScript type checking..."
npm run type-check

# Run linting
print_status "Running ESLint..."
npm run lint

# Build the application
print_status "Building Next.js application..."
npm run build

print_success "Next.js build completed successfully!"

# Check if build directory exists
if [ ! -d ".next" ]; then
    print_error "Build failed - .next directory not found"
    exit 1
fi

print_success "Build artifacts verified in .next directory"

# Display deployment information
echo ""
echo "🎉 Next.js Frontend Deployment Complete!"
echo ""
echo "📁 Project Structure:"
echo "   ├── src/app/           # Next.js pages"
echo "   ├── src/components/    # React components"
echo "   ├── src/lib/          # API clients & utilities"
echo "   ├── src/types/        # TypeScript definitions"
echo "   └── .next/            # Build output"
echo ""
echo "🔧 Available Commands:"
echo "   npm run dev           # Start development server"
echo "   npm run build         # Build for production"
echo "   npm run start         # Start production server"
echo "   npm run lint          # Run ESLint"
echo "   npm run type-check    # TypeScript checking"
echo ""
echo "🌐 API Integration:"
echo "   Production API: https://gold-braid-458901-v2.uc.r.appspot.com"
echo "   Mapbox Token: Configured"
echo "   Real Estate API: Configured"
echo ""
echo "🚀 To start the production server:"
echo "   npm start"
echo ""
echo "📱 To start development server:"
echo "   npm run dev"
echo "   Open http://localhost:3000"
echo ""

# Optional: Start development server
read -p "🤔 Would you like to start the development server now? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Starting development server..."
    npm run dev
fi

print_success "Deployment script completed successfully!"
