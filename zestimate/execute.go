package zestimate

import (
    "net/url"
    "propbolt/utils"
)

func GetRentZestimate(address string, compPropStatus *bool, distanceInMiles float64) (RentZestimateResponse, error) {
    return zestimateGo(address, compPropStatus, distanceInMiles, nil)
}

func GetRentZestimateWithProxy(address string, compPropStatus *bool, distanceInMiles float64, proxyURL *url.URL) (RentZestimateResponse, error) {
    return zestimateGo(address, compPropStatus, distanceInMiles, proxyURL)
}

func GetRentZestimateWithRotator(address string, compPropStatus *bool, distanceInMiles float64, rotator *utils.ProxyRotator) (RentZestimateResponse, error) {
    if rotator == nil || rotator.Count() == 0 {
        return zestimateGo(address, compPropStatus, distanceInMiles, nil)
    }

    // Try with different proxies on failure
    maxAttempts := 3
    for attempt := 0; attempt < maxAttempts; attempt++ {
        proxyURL := rotator.GetNext()
        result, err := zestimateGo(address, compPropStatus, distanceInMiles, proxyURL)

        // If successful, return result
        if err == nil {
            return result, nil
        }

        // If this was the last attempt, return the error
        if attempt == maxAttempts-1 {
            return RentZestimateResponse{}, err
        }

        // Log the failure and try next proxy
        // Note: In production, you might want to use a proper logger
    }

    return RentZestimateResponse{}, nil
}
