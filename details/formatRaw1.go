package details

import (
    "encoding/json"
    "fmt"
    "log"
)

/* bryce */

// Custom type to handle strings, arrays of strings, and numeric values
type StringOrArray []string

// UnmarshalJSON correctly handles unmarshalling strings, arrays of strings, and numeric values
func (s *StringOrArray) UnmarshalJSON(data []byte) error {
    // Try to unmarshal as a single string first
    var single string
    if err := json.Unmarshal(data, &single); err == nil {
        *s = StringOrArray{single}
        return nil
    }

    // Try to unmarshal as an array of strings
    var array []string
    if err := json.Unmarshal(data, &array); err == nil {
        *s = array
        return nil
    }

    // Try to unmarshal as a number and convert to string
    var number float64
    if err := json.Unmarshal(data, &number); err == nil {
        *s = StringOrArray{fmt.Sprintf("%.0f", number)}
        return nil
    }

    // Try to unmarshal as an integer and convert to string
    var integer int64
    if err := json.Unmarshal(data, &integer); err == nil {
        *s = StringOrArray{fmt.Sprintf("%d", integer)}
        return nil
    }

    return fmt.Errorf("failed to unmarshal StringOrArray: %s", data)
}

// MarshalJSON correctly handles marshalling into an array of strings
func (s StringOrArray) MarshalJSON() ([]byte, error) {
    if len(s) == 1 {
        return json.Marshal(s[0])
    }
    return json.Marshal([]string(s))
}

// Custom type to handle both float64 and string values
type FloatOrString struct {
    Value float64
    IsSet bool
}

// UnmarshalJSON implements the Unmarshaler interface for FloatOrString
func (f *FloatOrString) UnmarshalJSON(data []byte) error {
    // Try to unmarshal data as a float64
    var num float64
    if err := json.Unmarshal(data, &num); err == nil {
        f.Value = num
        f.IsSet = true
        return nil
    }

    // Try to unmarshal data as a string and convert to float64
    var str string
    if err := json.Unmarshal(data, &str); err == nil {
        var convertedNum float64
        if _, err := fmt.Sscanf(str, "%f", &convertedNum); err == nil {
            f.Value = convertedNum
            f.IsSet = true
            return nil
        }
    }

    // Log unexpected data format for inspection
    log.Printf("Unexpected data format: %s", string(data))
    return fmt.Errorf("data is neither a float64 nor a string that can be converted to float64")
}

// Function to extract only ResponsivePhotos from PropertyInfo
func ToResponsivePhotos(property PropertyInfo) ImagesOnly {
    return ImagesOnly{
        Address:          property.Address,
        ZipID:            property.ZipID,
        ResponsivePhotos: property.ResponsivePhotos,
    }
}
// Function to convert PropertyInfo to PropertyMinimalInfo
func ToMinimalInfo(property PropertyInfo) PropertyMinimalInfo {
    return PropertyMinimalInfo{
        Address:     property.Address,
        HomeStatus:  property.HomeStatus,
        Zestimate:   property.Zestimate,
        Bedrooms:    property.Bedrooms,
        Bathrooms:   property.Bathrooms,
        LivingArea:  property.ResoFacts.LivingArea,
        ZipID:       property.ZipID,
        Price:       property.Price,
        YearBuilt:   property.YearBuilt,
        DaysOnZillow: property.DaysOnZillow,
        MlsId:       property.AttributionInfo.MlsId,
        PropertyZillowURL: fmt.Sprintf("https://www.zillow.com/homedetails/%d_zpid/", property.ZipID),
		ResponsivePhotos: property.ResponsivePhotos,
    }
}

// PropertyMinimalInfo struct to hold minimal property information
type PropertyMinimalInfo struct {
    Address     Address       `json:"address"`
    HomeStatus  StringOrArray `json:"homeStatus"`
    Zestimate   int           `json:"zestimate"`
    Bedrooms    int           `json:"bedrooms"`
    Bathrooms   float32       `json:"bathrooms"`
    LivingArea  StringOrArray `json:"livingArea"`
    ZipID       int64         `json:"zpid"`
    Price       int           `json:"price"`
    YearBuilt   int           `json:"yearBuilt"`
    DaysOnZillow int          `json:"daysOnZillow"`
    MlsId       StringOrArray `json:"mlsId"`
    PropertyZillowURL string  `json:"propertyZillowURL"`
    ResponsivePhotos []ResponsivePhotosOriginalRatio `json:"responsivePhotos,omitempty"`
}

type ImagesOnly struct {
    Address          Address                        `json:"address"`
    ZipID            int64                          `json:"zpid"`
    ResponsivePhotos []ResponsivePhotosOriginalRatio `json:"responsivePhotos,omitempty"`
}
/* bryce end */

type bodyResponse struct {
	Props prod `json:"props"`
}
type prod struct {
	PageProps pageProps `json:"pageProps"`
}
type pageProps struct {
	Zpid           int64          `json:"zpid"`
	ComponentProps componentProps `json:"componentProps"`
}
type componentProps struct {
	GdpClientCache string `json:"gdpClientCache"`
}
type property struct {
	Property PropertyInfo `json:"property"`
}
type PropertyInfo struct {
	HomeStatus                            StringOrArray                    `json:"homeStatus"`
	Address                               Address                         `json:"address"`
	Bedrooms                              int                             `json:"bedrooms"`
	Bathrooms                             float32                         `json:"bathrooms"`
	YearBuilt                             int                             `json:"yearBuilt"`
	Currency                              StringOrArray                    `json:"currency"`
	CityId                  			  int        				  		`json:"cityId"`
	TimeOnZillow                          StringOrArray                     `json:"timeOnZillow"`
	Zestimate                             int                             `json:"zestimate"`
	Price								  int								`json:"price"`
	CountyId                  			  int        				  		`json:"countyId"`
	StateId                  			  int        				  		`json:"stateId"`
	ZipID                                 int64                           `json:"zpid"`
	Country                               StringOrArray                    `json:"country"`
	OpenHouseSchedule                     []OpenHouseSchedule              `json:"openHouseSchedule"`
	HdpUrl                                StringOrArray                    `json:"hdpUrl"`
	PropertyTaxRate                       float32                         `json:"propertyTaxRate"`
	Latitude                              float64                         `json:"latitude"`
	Longitude                             float64                         `json:"longitude"`
	//ImgSrc                                StringOrArray               	  `json:"imgSrc"`
	RentZestimate                         int                             `json:"rentZestimate"`
	ZestimateLowPercent					  StringOrArray					  `json:"zestimateLowPercent"` 
	ZestimateHighPercent				  StringOrArray					  `json:"zestimateHighPercent"`
	LastSoldPrice                         int                             `json:"lastSoldPrice"`
	AnnualHomeownersInsurance             float32                         `json:"annualHomeownersInsurance"`
	DaysOnZillow                          int                             `json:"daysOnZillow"`
	FavoriteCount                         int                             `json:"favoriteCount"`
	MonthlyHoaFee                         float32                         `json:"monthlyHoaFee"`
	LotSize                               int64                           `json:"lotSize"`
	LotAreaValue                          float32                         `json:"lotAreaValue"`
	LotAreaUnits                          StringOrArray                          `json:"lotAreaUnits"`
	PageViewCount                         int                             `json:"pageViewCount"`
	ParcelId                              StringOrArray                          `json:"parcelId"`
	BrokerageName                         string                          `json:"brokerageName"`
	Description                           StringOrArray                          `json:"description"`
	LivingAreaUnitsShort                  StringOrArray                          `json:"livingAreaUnitsShort"`
	VirtualTourUrl                        StringOrArray                          `json:"virtualTourUrl"`
	DatePostedString                      StringOrArray                          `json:"datePostedString"`
	PropertyTypeDimension                 StringOrArray                          `json:"propertyTypeDimension"`
	IsZillowOwned                         *bool                           `json:"isZillowOwned"`
	ForeclosureJudicialType               StringOrArray                          `json:"foreclosureJudicialType"`
	AttributionInfo                       AttributionInfo                 `json:"attributionInfo"`
	ResoFacts                             ResoFacts                       `json:"resoFacts"`
	MortgageRates                         MortgageRates                   `json:"mortgageRates"`
	PostingContact                        PostingContact                  `json:"postingContact"`
	ListingSubType                        ListingSubType                  `json:"listingSubType"`
	Listing_sub_type                      ListingSubType2                 `json:"listing_sub_type"`
	TimeZone 							  StringOrArray 				  `json:"timeZone"`
	ForeclosureTypes                      ForeclosureTypes                `json:"foreclosureTypes"`
	HomeInsights                          []HomeInsight                   `json:"homeInsights"`
	ListedBy                              []listedBy                      `json:"listedBy"`
	NearbyHomes                           []NearbyHomes                   `json:"nearbyHomes"`
	PriceHistory                          []PriceHistory                  `json:"priceHistory"`
	TaxHistory                            []TaxHistory                    `json:"taxHistory"`
	Schools                               []School                        `json:"schools"`
	//ResponsivePhotosOriginalRatio []ResponsivePhotosOriginalRatio  `json:"responsivePhotosOriginalRatio,omitempty"`
	ResponsivePhotos []ResponsivePhotosOriginalRatio `json:"responsivePhotos,omitempty"`
}
type Address struct {
	StreetAddress StringOrArray `json:"streetAddress"`
	City          StringOrArray `json:"city"`
	State         StringOrArray `json:"state"`
	Zipcode       StringOrArray `json:"zipcode"`
}
type ResoFacts struct {
	AccessibilityFeatures             StringOrArray        `json:"accessibilityFeatures"`
	AdditionalFeeInfo                 StringOrArray          `json:"additionalFeeInfo"`
	Associations                      []Association   `json:"associations"`
	AssociationFee                    StringOrArray          `json:"associationFee"`
	AssociationAmenities              StringOrArray        `json:"associationAmenities"`
	AssociationFee2                   any             `json:"associationFee2"`
	AssociationFeeIncludes            StringOrArray        `json:"associationFeeIncludes"`
	AssociationName                   StringOrArray          `json:"associationName"`
	AssociationName2                  StringOrArray          `json:"associationName2"`
	AssociationPhone                  StringOrArray          `json:"associationPhone"`
	AssociationPhone2                 StringOrArray          `json:"associationPhone2"`
	BasementYN                        *bool           `json:"basementYN"`
	BuildingName                      StringOrArray          `json:"buildingName"`
	BuyerAgencyCompensation           StringOrArray          `json:"buyerAgencyCompensation"`
	BuyerAgencyCompensationType       StringOrArray          `json:"buyerAgencyCompensationType"`
	Appliances                        StringOrArray        `json:"appliances"`
	AtAGlanceFacts                    []AtAGlanceFact `json:"atAGlanceFacts"`
	Attic                             StringOrArray          `json:"attic"`
	AvailabilityDate                  StringOrArray          `json:"availabilityDate"`
	Basement                          StringOrArray          `json:"basement"`
	Bathrooms                         float32         `json:"bathrooms"`
	BathroomsFull                     float32         `json:"bathroomsFull"`
	BathroomsHalf                     float32         `json:"bathroomsHalf"`
	BathroomsOneQuarter               *float32        `json:"bathroomsOneQuarter"`
	BathroomsPartial                  *float32        `json:"bathroomsPartial"`
	BathroomsFloat                    float64         `json:"bathroomsFloat"`
	BathroomsThreeQuarter             *float32        `json:"bathroomsThreeQuarter"`
	Bedrooms                          int             `json:"bedrooms"`
	BodyType                          StringOrArray          `json:"bodyType"`
	CanRaiseHorses                    *bool           `json:"canRaiseHorses"`
	CarportParkingCapacity            *int            `json:"carportParkingCapacity"`
	CityRegion                        StringOrArray          `json:"cityRegion"`
	CommonWalls                       StringOrArray          `json:"commonWalls"`
	CommunityFeatures                 StringOrArray        `json:"communityFeatures"`
	CompensationBasedOn               StringOrArray          `json:"compensationBasedOn"`
	Contingency                       StringOrArray          `json:"contingency"`
	Cooling                           StringOrArray        `json:"cooling"`
	CoveredParkingCapacity            int             `json:"coveredParkingCapacity"`
	CropsIncludedYN                   *bool           `json:"cropsIncludedYN"`
	CumulativeDaysOnMarket            StringOrArray          `json:"cumulativeDaysOnMarket"`
	DevelopmentStatus                 StringOrArray          `json:"developmentStatus"`
	DoorFeatures                      StringOrArray        `json:"doorFeatures"`
	Electric                          StringOrArray        `json:"electric"`
	Elevation                         StringOrArray          `json:"elevation"`
	ElevationUnits                    StringOrArray          `json:"elevationUnits"`
	EntryLevel                        StringOrArray          `json:"entryLevel"`
	EntryLocation                     StringOrArray          `json:"entryLocation"`
	Exclusions                        StringOrArray          `json:"exclusions"`
	FeesAndDues                       []FeesAndDues   `json:"feesAndDues"`
	Fencing                           StringOrArray          `json:"fencing"`
	FireplaceFeatures                 StringOrArray        `json:"fireplaceFeatures"`
	Fireplaces                        int             `json:"fireplaces"`
	Flooring                          StringOrArray        `json:"flooring"`
	FoundationArea                    StringOrArray          `json:"foundationArea"`
	Furnished                         *bool           `json:"furnished"`
	GarageParkingCapacity             int             `json:"garageParkingCapacity"`
	Gas                               StringOrArray          `json:"gas"`
	GreenBuildingVerificationType     StringOrArray          `json:"greenBuildingVerificationType"`
	GreenEnergyEfficient              StringOrArray        `json:"greenEnergyEfficient"`
	GreenEnergyGeneration             StringOrArray          `json:"greenEnergyGeneration"`
	GreenIndoorAirQuality             StringOrArray          `json:"greenIndoorAirQuality"`
	GreenSustainability               StringOrArray          `json:"greenSustainability"`
	GreenWaterConservation            StringOrArray        `json:"greenWaterConservation"`
	HasAssociation                    *bool           `json:"hasAssociation"`
	HasAttachedGarage                 *bool           `json:"hasAttachedGarage"`
	HasAttachedProperty               *bool           `json:"hasAttachedProperty"`
	HasCooling                        *bool           `json:"hasCooling"`
	HasCarport                        *bool           `json:"hasCarport"`
	HasElectricOnProperty             *bool           `json:"hasElectricOnProperty"`
	HasFireplace                      *bool           `json:"hasFireplace"`
	HasGarage                         *bool           `json:"hasGarage"`
	HasHeating                        *bool           `json:"hasHeating"`
	HasLandLease                      *bool           `json:"hasLandLease"`
	HasOpenParking                    *bool           `json:"hasOpenParking"`
	HasSpa                            *bool           `json:"hasSpa"`
	HasPrivatePool                    *bool           `json:"hasPrivatePool"`
	HasView                           *bool           `json:"hasView"`
	HasWaterfrontView                 *bool           `json:"hasWaterfrontView"`
	Heating                           StringOrArray        `json:"heating"`
	HighSchool                        StringOrArray          `json:"highSchool"`
	HighSchoolDistrict                StringOrArray          `json:"highSchoolDistrict"`
	HoaFee                            StringOrArray          `json:"hoaFee"`
	HoaFeeTotal                       StringOrArray          `json:"hoaFeeTotal"`
	HomeType                          StringOrArray          `json:"homeType"`
	HorseAmenities                    StringOrArray          `json:"horseAmenities"`
	HorseYN                           *bool           `json:"horseYN"`
	InteriorFeatures                  StringOrArray        `json:"interiorFeatures"`
	IrrigationWaterRightsAcres        *float64        `json:"irrigationWaterRightsAcres"`
	IrrigationWaterRightsYN           *bool           `json:"irrigationWaterRightsYN"`
	IsSeniorCommunity                 *bool           `json:"isSeniorCommunity"`
	LandLeaseAmount                   FloatOrString        `json:"landLeaseAmount"`
	LandLeaseExpirationDate           StringOrArray          `json:"landLeaseExpirationDate"`
	LaundryFeatures                   StringOrArray        `json:"laundryFeatures"`
	Levels                            StringOrArray          `json:"levels"`
	ListingId                         StringOrArray          `json:"listingId"`
	LotFeatures                       StringOrArray        `json:"lotFeatures"`
	LotSize                           StringOrArray          `json:"lotSize"`
	LivingQuarters                    StringOrArray        `json:"livingQuarters"`
	MainLevelBathrooms                *float32        `json:"mainLevelBathrooms"`
	MainLevelBedrooms                 *int            `json:"mainLevelBedrooms"`
	MarketingType                     StringOrArray          `json:"marketingType"`
	MiddleOrJuniorSchool              StringOrArray          `json:"middleOrJuniorSchool"`
	MiddleOrJuniorSchoolDistrict      StringOrArray          `json:"middleOrJuniorSchoolDistrict"`
	Municipality                      StringOrArray          `json:"municipality"`
	NumberOfUnitsInCommunity          *int            `json:"numberOfUnitsInCommunity"`
	OfferReviewDate                   StringOrArray          `json:"offerReviewDate"`
	OnMarketDate                      int64           `json:"onMarketDate"`
	OpenParkingCapacity               *int            `json:"openParkingCapacity"`
	OtherEquipment                    StringOrArray        `json:"otherEquipment"`
    OtherFacts                        []interface{}   `json:"otherFacts"`  // Updated field to handle array of any type
	OtherParking                      StringOrArray         `json:"otherParking"`
	OwnershipType                     StringOrArray          `json:"ownershipType"`
	ParkingCapacity                   int             `json:"parkingCapacity"`
	ParkingFeatures                   StringOrArray        `json:"parkingFeatures"`
	PatioAndPorchFeatures             StringOrArray        `json:"patioAndPorchFeatures"`
	PoolFeatures                      StringOrArray        `json:"poolFeatures"`
	PricePerSquareFoot                int             `json:"pricePerSquareFoot"`
	RoadSurfaceType                   StringOrArray        `json:"roadSurfaceType"`
	RoofType                          StringOrArray          `json:"roofType"`
	Rooms                             []Room          `json:"rooms"`
	SecurityFeatures                  StringOrArray        `json:"securityFeatures"`
	Sewer                             StringOrArray        `json:"sewer"`
	SpaFeatures                       StringOrArray        `json:"spaFeatures"`
	SpecialListingConditions          StringOrArray          `json:"specialListingConditions"`
	Stories                           *int            `json:"stories"`
	StoriesTotal                      *int            `json:"storiesTotal"`
	SubAgencyCompensation             StringOrArray          `json:"subAgencyCompensation"`
	SubAgencyCompensationType         StringOrArray          `json:"subAgencyCompensationType"`
	SubdivisionName                   StringOrArray          `json:"subdivisionName"`
	TotalActualRent                   *float64        `json:"totalActualRent"`
	TransactionBrokerCompensation     StringOrArray          `json:"transactionBrokerCompensation"`
	TransactionBrokerCompensationType StringOrArray          `json:"transactionBrokerCompensationType"`
	Utilities                         StringOrArray        `json:"utilities"`
	View                              StringOrArray        `json:"view"`
	WaterSource                       StringOrArray        `json:"waterSource"`
	WaterBodyName                     StringOrArray          `json:"waterBodyName"`
	WaterfrontFeatures                StringOrArray        `json:"waterfrontFeatures"`
	WaterView                         StringOrArray          `json:"waterView"`
	WaterViewYN                       *bool           `json:"waterViewYN"`
	WindowFeatures                    StringOrArray        `json:"windowFeatures"`
	YearBuilt                         int             `json:"yearBuilt"`
	Zoning                            StringOrArray          `json:"zoning"`
	ZoningDescription                 StringOrArray          `json:"zoningDescription"`
	AboveGradeFinishedArea            StringOrArray          `json:"aboveGradeFinishedArea"`
	AdditionalParcelsDescription      StringOrArray          `json:"additionalParcelsDescription"`
	ArchitecturalStyle                StringOrArray          `json:"architecturalStyle"`
	BelowGradeFinishedArea            StringOrArray          `json:"belowGradeFinishedArea"`
	BuilderModel                      StringOrArray          `json:"builderModel"`
	BuilderName                       StringOrArray          `json:"builderName"`
	BuildingArea                      StringOrArray          `json:"buildingArea"`
	BuildingAreaSource                StringOrArray          `json:"buildingAreaSource"`
	BuildingFeatures                  StringOrArray          `json:"buildingFeatures"`
	ConstructionMaterials             StringOrArray        `json:"constructionMaterials"`
	ExteriorFeatures                  StringOrArray        `json:"exteriorFeatures"`
	FoundationDetails                 StringOrArray        `json:"foundationDetails"`
	FrontageLength                    StringOrArray          `json:"frontageLength"`
	FrontageType                      StringOrArray          `json:"frontageType"`
	HasAdditionalParcels              *bool           `json:"hasAdditionalParcels"`
	HasPetsAllowed                    *bool           `json:"hasPetsAllowed"`
	HasRentControl                    *bool           `json:"hasRentControl"`
	HasHomeWarranty                   *bool           `json:"hasHomeWarranty"`
	Inclusions                        StringOrArray        `json:"inclusions"`
	IncomeIncludes                    StringOrArray          `json:"incomeIncludes"`
	IsNewConstruction                 *bool           `json:"isNewConstruction"`
	ListingTerms                      StringOrArray          `json:"listingTerms"`
	LivingAreaRange                   StringOrArray          `json:"livingAreaRange"`
	LivingAreaRangeUnits              StringOrArray          `json:"livingAreaRangeUnits"`
	LivingArea                        StringOrArray          `json:"livingArea"`
	LotSizeDimensions                 StringOrArray          `json:"lotSizeDimensions"`
	NumberOfUnitsVacant               *int            `json:"numberOfUnitsVacant"`
	OtherStructures                   StringOrArray        `json:"otherStructures"`
	Ownership                         StringOrArray          `json:"ownership"`
	ParcelNumber                      StringOrArray          `json:"parcelNumber"`
	PropertyCondition                 StringOrArray          `json:"propertyCondition"`
	PropertySubType                   StringOrArray        `json:"propertySubType"`
	StructureType                     StringOrArray          `json:"structureType"`
	Topography                        StringOrArray          `json:"topography"`
	Vegetation                        StringOrArray        `json:"vegetation"`
	WoodedArea                        StringOrArray          `json:"woodedArea"`
	YearBuiltEffective                *int            `json:"yearBuiltEffective"`
	VirtualTour                       StringOrArray          `json:"virtualTour"`
	ElementarySchool                  StringOrArray          `json:"elementarySchool"`
	ElementarySchoolDistrict          StringOrArray          `json:"elementarySchoolDistrict"`
	ListAOR                           StringOrArray          `json:"listAOR"`
}
// New struct for OpenHouseSchedule
type OpenHouseSchedule struct {
	StartTime string `json:"startTime"`
	EndTime   string `json:"endTime"`
}
type AtAGlanceFact struct {
	FactLabel StringOrArray `json:"factLabel"`
	FactValue StringOrArray `json:"factValue"` // Using pointer to handle null values
}
type AttributionInfo struct {
	ListingAgreement             StringOrArray          `json:"listingAgreement"`
	MlsName                      StringOrArray          `json:"mlsName"`
	AgentEmail                   StringOrArray          `json:"agentEmail"`
	AgentLicenseNumber           StringOrArray          `json:"agentLicenseNumber"`
	AgentName                    StringOrArray          `json:"agentName"`
	AgentPhoneNumber             StringOrArray          `json:"agentPhoneNumber"`
	AttributionTitle             StringOrArray          `json:"attributionTitle"`
	BrokerName                   string      		    `json:"brokerName"`
	BrokerPhoneNumber            StringOrArray          `json:"brokerPhoneNumber"`
	BuyerAgentMemberStateLicense StringOrArray          `json:"buyerAgentMemberStateLicense"`
	BuyerAgentName               StringOrArray          `json:"buyerAgentName"`
	BuyerBrokerageName           StringOrArray          `json:"buyerBrokerageName"`
	CoAgentLicenseNumber         StringOrArray          `json:"coAgentLicenseNumber"`
	CoAgentName                  StringOrArray          `json:"coAgentName"`
	CoAgentNumber                StringOrArray          `json:"coAgentNumber"`
	LastChecked                  StringOrArray          `json:"lastChecked"`
	LastUpdated                  StringOrArray          `json:"lastUpdated"`
	ListingOffices               []ListingOffice `json:"listingOffices"`
	ListingAgents                []ListingAgent  `json:"listingAgents"`
	MlsDisclaimer                StringOrArray          `json:"mlsDisclaimer"`
	MlsId                        StringOrArray          `json:"mlsId"`
	ProviderLogo                 StringOrArray          `json:"providerLogo"`
	InfoString3                  StringOrArray          `json:"infoString3"`
	InfoString5                  StringOrArray          `json:"infoString5"`
	InfoString10                 StringOrArray          `json:"infoString10"`
	InfoString16                 StringOrArray          `json:"infoString16"`
	TrueStatus                   StringOrArray          `json:"trueStatus"`
}
type ListingOffice struct {
	AssociatedOfficeType StringOrArray `json:"associatedOfficeType"`
	OfficeName           StringOrArray `json:"officeName"`
}

type ListingAgent struct {
	AssociatedAgentType StringOrArray `json:"associatedAgentType"`
	MemberFullName      StringOrArray `json:"memberFullName"`
	MemberStateLicense  StringOrArray `json:"memberStateLicense"`
}
type School struct {
	Link               StringOrArray  `json:"link"`
	Rating             int     `json:"rating"`
	TotalCount         StringOrArray  `json:"totalCount"`
	Distance           float32 `json:"distance"`
	Assigned           StringOrArray  `json:"assigned"`
	Name               StringOrArray  `json:"name"`
	StudentsPerTeacher StringOrArray  `json:"studentsPerTeacher"`
	IsAssigned         StringOrArray  `json:"isAssigned"`
	Size               StringOrArray  `json:"size"`
	Level              StringOrArray  `json:"level"`
	Grades             StringOrArray  `json:"grades"`
	Type               StringOrArray  `json:"type"`
}
type TaxHistory struct {
	Time              int64   `json:"time"`
	TaxPaid           float32 `json:"taxPaid"`
	TaxIncreaseRate   float32 `json:"taxIncreaseRate"`
	Value             float32 `json:"value"`
	ValueIncreaseRate float32 `json:"valueIncreaseRate"`
}
// Corrected NearbyHomes struct
type NearbyHomes struct {
	LivingArea           float32       `json:"livingArea"`
	LivingAreaValue      int64         `json:"livingAreaValue"`
	LotAreaUnits         StringOrArray `json:"lotAreaUnits"`
	LotAreaValue         float32       `json:"lotAreaValue"`
	LotSize              int64         `json:"lotSize"`
	MiniCardPhotos       []Photo       `json:"miniCardPhotos"`
	Zpid                 int64         `json:"zpid"`
	Longitude            float64       `json:"longitude"`
	LivingAreaUnitsShort StringOrArray `json:"livingAreaUnitsShort"`
	Price                float32       `json:"price"`
	HomeType             StringOrArray `json:"homeType"`
	HomeStatus           StringOrArray `json:"homeStatus"`
	Currency             StringOrArray `json:"currency"`
	Latitude             float64       `json:"latitude"`
	Address              Address       `json:"address"`
}
type PriceHistory struct {
	Date               StringOrArray  `json:"date"`
	Time               int64   `json:"time"`
	Price              float32 `json:"price"`
	PricePerSquareFoot float32 `json:"pricePerSquareFoot"`
	PriceChangeRate    float32 `json:"priceChangeRate"`
	Event              StringOrArray  `json:"event"`
	Source             StringOrArray  `json:"source"`
	BuyerAgent         Agent   `json:"buyerAgent"`
	SellerAgent        Agent   `json:"sellerAgent"`
}
type Agent struct {
	ProfileUrl StringOrArray `json:"profileUrl"`
	Name       StringOrArray `json:"name"`
	Photo      Photo  `json:"photo"`
}
type Photo struct {
	URL StringOrArray `json:"url"`
}
type listedBy struct {
	ID       StringOrArray    `json:"id"`
	Elements []Element `json:"elements"`
}
type Element struct {
	ID   StringOrArray `json:"id"`
	Text StringOrArray `json:"text"`
}
type HomeInsight struct {
	HomeInsights []HomeInsightValues `json:"insights"`
}
type HomeInsightValues struct {
	Phrases StringOrArray `json:"phrases"`
}
type ForeclosureTypes struct {
	IsBankOwned         *bool `json:"isBankOwned"`
	IsForeclosedNFS     *bool `json:"isForeclosedNFS"`
	IsPreforeclosure    *bool `json:"isPreforeclosure"`
	IsAnyForeclosure    *bool `json:"isAnyForeclosure"`
	WasNonRetailAuction *bool `json:"wasNonRetailAuction"`
	WasForeclosed       *bool `json:"wasForeclosed"`
	WasREO              *bool `json:"wasREO"`
	WasDefault          *bool `json:"wasDefault"`
}
type ListingSubType struct {
	IsFSBA        *bool `json:"isFSBA"`
	IsFSBO        *bool `json:"isFSBO"`
	IsPending     *bool `json:"isPending"`
	IsNewHome     *bool `json:"isNewHome"`
	IsForeclosure *bool `json:"isForeclosure"`
	IsBankOwned   *bool `json:"isBankOwned"`
	IsForAuction  *bool `json:"isForAuction"`
	IsOpenHouse   *bool `json:"isOpenHouse"`
	IsComingSoon  *bool `json:"isComingSoon"`
}
type ListingSubType2 struct {
	IsFSBA        *bool `json:"is_FSBA"`
	IsFSBO        *bool `json:"is_FSBO"`
	IsPending     *bool `json:"is_pending"`
	IsNewHome     *bool `json:"is_newHome"`
	IsForeclosure *bool `json:"is_foreclosure"`
	IsBankOwned   *bool `json:"is_bankOwned"`
	IsForAuction  *bool `json:"is_forAuction"`
	IsOpenHouse   *bool `json:"is_openHouse"`
	IsComingSoon  *bool `json:"is_comingSoon"`
}
type PostingContact struct {
	Name  StringOrArray `json:"name"`
	Photo StringOrArray `json:"photo"`
}
type ResponsivePhotosOriginalRatio struct {
	MixedSources MixedSources `json:"mixedSources"`
}
type MixedSources struct {
//	Webp []Img `json:"webp"`
	JPEG []Img `json:"jpeg"`
}
type Img struct {
	URL   StringOrArray `json:"url"`
	Width int    `json:"width"`
}
type MortgageRates struct {
	ThirtyYearFixedRate float32 `json:"thirtyYearFixedRate"`
}
type FeesAndDues struct {
	Type  StringOrArray `json:"type"`
	Fee   StringOrArray `json:"fee"`
	Name  StringOrArray `json:"name"`
	Phone StringOrArray `json:"phone"`
}
type Room struct {
	Aea                   StringOrArray `json:"area"`
	Description           StringOrArray `json:"description"`
	Dimensions            StringOrArray `json:"dimensions"`
	Level                 StringOrArray `json:"level"`
	Features              StringOrArray `json:"features"`
	RoomArea              StringOrArray `json:"roomArea"`
	RoomAreaSource        StringOrArray `json:"roomAreaSource"`
	RoomAreaUnits         StringOrArray `json:"roomAreaUnits"`
	RoomDescription       StringOrArray `json:"roomDescription"`
	RoomDimensions        StringOrArray `json:"roomDimensions"`
    RoomFeatures          StringOrArray `json:"roomFeatures"`
	RoomLength            StringOrArray `json:"roomLength"`
	RoomLengthWidthSource StringOrArray `json:"roomLengthWidthSource"`
	RoomLengthWidthUnits  StringOrArray `json:"roomLengthWidthUnits"`
	RoomLevel             StringOrArray `json:"roomLevel"`
	RoomType              StringOrArray `json:"roomType"`
	RoomWidth             StringOrArray `json:"roomWidth"`
}
type Association struct {
	FeeFrequency StringOrArray `json:"feeFrequency"`
	Name         StringOrArray `json:"name"`
	Phone        StringOrArray `json:"phone"`
}
