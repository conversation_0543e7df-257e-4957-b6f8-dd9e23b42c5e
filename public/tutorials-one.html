<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML SOURCE CODE EXAMPLE</title>
    <style>
        body { font-family: monospace; } /* Use a monospace font for code */

        pre {
            background-color: #f0f0f0; /* Light background color */
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto; /* Enable horizontal scrolling if needed */
            white-space: pre-wrap; /* Preserve line breaks and wrap long lines */
        }

        /* Syntax Highlighting */
        .html-tag { color: blue; }        /* HTML tags (e.g., <div>, <span>) */
        .html-attr-name { color: red; }  /* Attribute names (e.g., class, id) */
        .html-attr-value { color: green; }/* Attribute values (e.g., "container") */
        .css-selector { color: brown; }  /* CSS selectors (e.g., body, .class) */
        .css-property { color: teal; }   /* CSS properties (e.g., font-family) */
        .css-value { color: purple; }    /* CSS values (e.g., Arial, 10px) */
    </style>
</head>
<body>
 <h3>Tutorial 1: How to Scrape Zillow Listings with Our API: Build Real Estate Apps Faster</h3>
    <h2>HTML Source Code:</h2>
<H4>Note: This API is now publicly accessible without authentication.<br>
    No API key required - all endpoints are open for testing and development.</H4>
    <h4>Direct API access - no headers needed</h4>
    <pre>

        &lt;!DOCTYPE html&gt;
        &lt;html lang="en"&gt;
        &lt;head&gt;
            &lt;meta charset="UTF-8"&gt;
            &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
            &lt;title&gt;Zillow Property Search&lt;/title&gt;
            &lt;style&gt;
                body {
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 0;
                }
                .container {
                    max-width: 600px;
                    margin: 0 auto;
                }
                .input-group, .result-group, .output {
                    margin-bottom: 15px;
                }
                .input-group label {
                    display: block;
                    margin-bottom: 5px;
                }
                .input-group input {
                    width: 100%;
                    padding: 10px;
                    box-sizing: border-box;
                }
                .result-group {
                    margin-top: 20px;
                }
                .result-group div {
                    margin-bottom: 10px;
                }
                .navbar {
                    background-color: #121c24;
                    padding: 10px 0;
                    text-align: center;
                    border-bottom: 1px solid #ccc;
                }
                .navbar img {
                    height: 50px;
                }
                .fetch {
                    margin-top: 15px;
                }
            &lt;/style&gt;
        &lt;/head&gt;
        &lt;body&gt;
            &lt;div class="navbar"&gt;
                &lt;img src="/byte-media-logo-v2.png" alt="Byte Media Logo"&gt;
            &lt;/div&gt;
            &lt;div class="container"&gt;
                &lt;h1&gt;Zillow Property Search&lt;/h1&gt;
                &lt;div class="input-group"&gt;
                    &lt;label for="zillowUrl"&gt;Basic Zillow Listing Search:&lt;/label&gt;
                    &lt;input type="text" id="zillowUrl" placeholder="Enter Zillow URL"&gt;
                    &lt;button class="fetch" onclick="fetchPropertyDetails()"&gt;Fetch Details&lt;/button&gt;
                &lt;/div&gt;
                &lt;div class="result-group"&gt;
                    &lt;div&gt;Listing Status: &lt;span id="listingStatus"&gt;&lt;/span&gt;&lt;/div&gt;
                    &lt;div&gt;Price: &lt;span id="price"&gt;&lt;/span&gt;&lt;/div&gt;
                    &lt;div&gt;Time On Zillow: &lt;span id="timeOnZillow"&gt;&lt;/span&gt;&lt;/div&gt;
                    &lt;div&gt;Address: &lt;span id="address"&gt;&lt;/span&gt;&lt;/div&gt;
                    &lt;div&gt;City: &lt;span id="city"&gt;&lt;/span&gt;&lt;/div&gt;
                    &lt;div&gt;State: &lt;span id="state"&gt;&lt;/span&gt;&lt;/div&gt;
                    &lt;div&gt;Zip: &lt;span id="zip"&gt;&lt;/span&gt;&lt;/div&gt;
                    &lt;div&gt;Bedrooms: &lt;span id="bedrooms"&gt;&lt;/span&gt;&lt;/div&gt;
                    &lt;div&gt;Bathrooms: &lt;span id="bathrooms"&gt;&lt;/span&gt;&lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;script&gt;
                async function fetchPropertyDetails() {
                    const zillowUrl = document.getElementById('zillowUrl').value;
                    const encodedUrl = encodeURIComponent(zillowUrl);
                    const apiUrl = `/property?url=${encodedUrl}&listingPhotos=false`;

                    const response = await fetch(apiUrl, {
                        method: 'GET'
                    });
                    const result = await response.json();
                    document.getElementById('listingStatus').innerText = result.homeStatus || 'N/A';
                    document.getElementById('price').innerText = result.price || 'N/A';
                    document.getElementById('address').innerText = result.address.streetAddress || 'N/A';
                    document.getElementById('city').innerText = result.address.city || 'N/A';
                    document.getElementById('state').innerText = result.address.state || 'N/A';
                    document.getElementById('zip').innerText = result.address.zipcode || 'N/A';
                    document.getElementById('bedrooms').innerText = result.bedrooms || 'N/A';
                    document.getElementById('bathrooms').innerText = result.bathrooms || 'N/A';
                    document.getElementById('timeOnZillow').innerText = result.timeOnZillow || 'N/A';
                }
            &lt;/script&gt;
        &lt;/body&gt;
        &lt;/html&gt;
        
    </pre>

</body>
</html>