<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zillow - Powerful API: cURL Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }

        /* Navbar Styling */
        .navbar {
            background-color: #121c24;
            padding: 10px 0;
            text-align: center;
            border-bottom: 1px solid #ccc; /* Add a subtle border */
        }

        .navbar img {
            max-height: 50px;
        }

        /* Main Content Styling */
        .container {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff; /* Light background for better contrast */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* Subtle shadow */
        }

        /* Input and Output Styling */
        .input-group, .result-group, .output {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
        }

        .input-group input, .input-group textarea {
            width: 100%;
            padding: 10px;
            box-sizing: border-box;
            border: 1px solid #ccc; /* Add a border */
            border-radius: 4px;   /* Slightly rounded corners */
        }

        .input-group button {
            padding: 10px 20px;
            background-color: #007bff;
            color: #fff;
            border: none;
            cursor: pointer;
        }

        .input-group button:hover {
            background-color: #0056b3;
        }

        .loading-icon {
            display: none;
            text-align: center;
            font-weight: bold;
        }

        #output { /* Style for the JSON output area */
            font-family: monospace; /* Use a monospace font for better readability */
            white-space: pre; /* Preserve whitespace (for indentation) */
            overflow-x: auto; /* Add horizontal scrollbar if needed */
            padding: 15px;
            background-color: #f5f5f5; /* Light background for better contrast */
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="navbar">
        <img src="/byte-media-logo-v2.png" alt="Byte Media Logo">
    </div>
    <div class="container">
        <h1>cURL Input</h1>
        <p>cURL allows you to effortlessly request and explore API's endpoints with JSON outputs.</p>

        <p>Note: This API is now publicly accessible without authentication. <br>
        No API key required - simply use the local endpoints directly.</p>
        <div class="input-group">
            <label for="curlCommand">Enter Curl Command (no auth headers needed):</label>
            <textarea rows="15" id="curlCommand" placeholder="curl 'http://localhost:8080/property?address=123 Main St'"></textarea>
        </div>

        <div class="input-group">
            <button onclick="executeCurl()">Execute Curl</button>
            <div class="loading-icon" id="loadingIcon">Loading...</div>
        </div>
        <div class="output" id="output"></div>
    </div>
    <script>
        async function executeCurl() {
            const curlCommand = document.getElementById('curlCommand').value;

            // Extract URL from curl command - support both --url and direct URL formats
            const urlRegex = /(?:--url\s+['"]([^'"]+)['"]|curl\s+['"]([^'"]+)['"]|curl\s+([^\s]+))/;
            const urlMatch = curlCommand.match(urlRegex);

            if (!urlMatch) {
                document.getElementById('output').innerText = 'Error: Could not extract URL from cURL command. Please use format: curl "http://localhost:8080/property?address=123 Main St"';
                return;
            }

            // Get the URL from whichever capture group matched
            const apiUrl = urlMatch[1] || urlMatch[2] || urlMatch[3];

            const options = {
                method: 'GET'
                // No authentication headers needed
            };

            const loadingIcon = document.getElementById('loadingIcon');
            loadingIcon.style.display = 'block';
            try {
                const response = await fetch(apiUrl, options);
                const result = await response.json();

                //document.getElementById('output').innerText = JSON.stringify(result, null, 2);
                 // Improved JSON formatting with indentation
                 document.getElementById('output').innerText = JSON.stringify(result, null, 4); 
            } catch (error) {
                console.error(error);
                document.getElementById('output').innerText = 'Error: ' + error.message;
            } finally {
                loadingIcon.style.display = 'none';
            }
        }
    </script>
</body>
</html>
