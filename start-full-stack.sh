#!/bin/bash

# 🚀 Full Stack Startup Script for Vacant Land Search Platform
# This script starts the Go backend first, then the Next.js frontend

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
BACKEND_PORT=8080
FRONTEND_PORT=3000
BACKEND_BINARY="propbolt"
BACKEND_PID_FILE=".backend.pid"
FRONTEND_PID_FILE=".frontend.pid"

# Function to print colored output
print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to kill process on port
kill_port() {
    local port=$1
    local pids=$(lsof -ti:$port)
    if [ ! -z "$pids" ]; then
        print_warning "Killing existing processes on port $port"
        echo $pids | xargs kill -9
        sleep 2
    fi
}

# Function to cleanup on exit
cleanup() {
    print_header "🧹 Cleaning up processes..."
    
    # Kill backend
    if [ -f "$BACKEND_PID_FILE" ]; then
        local backend_pid=$(cat $BACKEND_PID_FILE)
        if kill -0 $backend_pid 2>/dev/null; then
            print_status "Stopping Go backend (PID: $backend_pid)"
            kill $backend_pid
        fi
        rm -f $BACKEND_PID_FILE
    fi
    
    # Kill frontend
    if [ -f "$FRONTEND_PID_FILE" ]; then
        local frontend_pid=$(cat $FRONTEND_PID_FILE)
        if kill -0 $frontend_pid 2>/dev/null; then
            print_status "Stopping Next.js frontend (PID: $frontend_pid)"
            kill $frontend_pid
        fi
        rm -f $FRONTEND_PID_FILE
    fi
    
    # Kill any remaining processes on our ports
    kill_port $BACKEND_PORT
    kill_port $FRONTEND_PORT
    
    print_success "Cleanup completed"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM EXIT

print_header "🏖️ Starting Vacant Land Search Full Stack Platform"

# Check prerequisites
print_status "Checking prerequisites..."

# Check Go
if ! command -v go &> /dev/null; then
    print_error "Go is not installed. Please install Go first."
    exit 1
fi
print_success "Go version: $(go version)"

# Check Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18+ is required. Current version: $(node --version)"
    exit 1
fi
print_success "Node.js version: $(node --version)"

# Check npm
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi
print_success "npm version: $(npm --version)"

# Clean up any existing processes
print_status "Cleaning up existing processes..."
kill_port $BACKEND_PORT
kill_port $FRONTEND_PORT

# ================================
# BACKEND SETUP AND START
# ================================
print_header "🔧 Setting up Go Backend"

# Check if binary exists, if not build it
if [ ! -f "$BACKEND_BINARY" ]; then
    print_status "Building Go backend..."
    go mod tidy
    go build -o $BACKEND_BINARY
    if [ $? -ne 0 ]; then
        print_error "Failed to build Go backend"
        exit 1
    fi
    print_success "Go backend built successfully"
else
    print_status "Using existing Go binary: $BACKEND_BINARY"
fi

# Set environment variables for backend
export PORT=$BACKEND_PORT
export DATABASE_URL=${DATABASE_URL:-"postgresql://wonderland_owner:<EMAIL>/wonderland?sslmode=require"}

# Set proxy URLs if available
if [ ! -z "$PROXY_URLS" ]; then
    print_status "Using configured proxy URLs"
    export PROXY_URLS=$PROXY_URLS
else
    print_warning "No proxy URLs configured. Backend will run without proxies."
fi

print_status "Starting Go backend on port $BACKEND_PORT..."
./$BACKEND_BINARY &
BACKEND_PID=$!
echo $BACKEND_PID > $BACKEND_PID_FILE

# Wait for backend to start
print_status "Waiting for backend to start..."
for i in {1..30}; do
    if check_port $BACKEND_PORT; then
        print_success "Go backend started successfully (PID: $BACKEND_PID)"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "Backend failed to start within 30 seconds"
        exit 1
    fi
    sleep 1
done

# Test backend health
print_status "Testing backend health..."
if curl -s http://localhost:$BACKEND_PORT/status > /dev/null; then
    print_success "Backend health check passed"
else
    print_warning "Backend health check failed, but continuing..."
fi

# ================================
# FRONTEND SETUP AND START
# ================================
print_header "⚛️ Setting up Next.js Frontend"

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    print_status "Installing Node.js dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        print_error "Failed to install Node.js dependencies"
        exit 1
    fi
    print_success "Dependencies installed successfully"
else
    print_status "Using existing node_modules"
fi

# Build Next.js application
print_status "Building Next.js application..."
npm run build
if [ $? -ne 0 ]; then
    print_error "Failed to build Next.js application"
    exit 1
fi
print_success "Next.js application built successfully"

print_status "Starting Next.js frontend on port $FRONTEND_PORT..."
npm start &
FRONTEND_PID=$!
echo $FRONTEND_PID > $FRONTEND_PID_FILE

# Wait for frontend to start
print_status "Waiting for frontend to start..."
for i in {1..60}; do
    if check_port $FRONTEND_PORT; then
        print_success "Next.js frontend started successfully (PID: $FRONTEND_PID)"
        break
    fi
    if [ $i -eq 60 ]; then
        print_error "Frontend failed to start within 60 seconds"
        exit 1
    fi
    sleep 1
done

# ================================
# SUCCESS SUMMARY
# ================================
print_header "🎉 Full Stack Platform Started Successfully!"

echo ""
echo -e "${GREEN}🌐 Application URLs:${NC}"
echo -e "   ${CYAN}Frontend (Next.js):${NC} http://localhost:$FRONTEND_PORT"
echo -e "   ${CYAN}Backend (Go API):${NC}   http://localhost:$BACKEND_PORT"
echo ""
echo -e "${GREEN}📊 Service Status:${NC}"
echo -e "   ${CYAN}Go Backend:${NC}       Running (PID: $BACKEND_PID)"
echo -e "   ${CYAN}Next.js Frontend:${NC} Running (PID: $FRONTEND_PID)"
echo ""
echo -e "${GREEN}🔗 Quick Links:${NC}"
echo -e "   ${CYAN}Dashboard:${NC}        http://localhost:$FRONTEND_PORT"
echo -e "   ${CYAN}Land Search:${NC}      http://localhost:$FRONTEND_PORT/search"
echo -e "   ${CYAN}API Status:${NC}       http://localhost:$BACKEND_PORT/status"
echo -e "   ${CYAN}API Health:${NC}       http://localhost:$BACKEND_PORT"
echo ""
echo -e "${GREEN}🛠️ Development Tools:${NC}"
echo -e "   ${CYAN}Backend Logs:${NC}     tail -f backend.log (if logging to file)"
echo -e "   ${CYAN}Frontend Logs:${NC}    Check terminal output"
echo -e "   ${CYAN}Kill Processes:${NC}   Ctrl+C or kill script"
echo ""
echo -e "${YELLOW}💡 Tips:${NC}"
echo -e "   • Frontend connects to backend automatically"
echo -e "   • Real-time property data sync available"
echo -e "   • Mapbox integration with property boundaries"
echo -e "   • All API endpoints are accessible"
echo ""
echo -e "${PURPLE}Press Ctrl+C to stop both services${NC}"

# Keep script running and monitor processes
while true; do
    # Check if backend is still running
    if ! kill -0 $BACKEND_PID 2>/dev/null; then
        print_error "Backend process died unexpectedly"
        exit 1
    fi
    
    # Check if frontend is still running
    if ! kill -0 $FRONTEND_PID 2>/dev/null; then
        print_error "Frontend process died unexpectedly"
        exit 1
    fi
    
    sleep 5
done
