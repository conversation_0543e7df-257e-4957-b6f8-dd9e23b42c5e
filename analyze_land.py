#!/usr/bin/env python3
import json
import requests
from datetime import datetime
from typing import List, Dict, Any

class LandAnalyzer:
    def __init__(self):
        self.all_listings = []
        self.base_url = "http://localhost:8080"
        
    def search_land(self, page: int = 1) -> Dict[str, Any]:
        """Search for land in Daytona Beach area"""
        params = {
            "neLat": 29.3,
            "neLong": -80.9,
            "swLat": 29.1,
            "swLong": -81.1,
            "isLotLand": "false",  # Get all properties and filter client-side
            "zoom": 11,
            "pagination": page
        }
        response = requests.get(f"{self.base_url}/search", params=params)
        return response.json()
    
    def get_property_details(self, zpid: str) -> Dict[str, Any]:
        """Get detailed property information"""
        params = {"id": zpid}
        try:
            response = requests.get(f"{self.base_url}/property", params=params)
            if response.status_code == 200:
                return response.json()
        except:
            pass
        return {}
    
    def collect_all_listings(self, max_pages: int = 10):
        """Collect listings from multiple pages"""
        print(f"Collecting land listings from Daytona Beach area...")
        
        for page in range(1, max_pages + 1):
            print(f"Fetching page {page}...")
            data = self.search_land(page)
            
            if "listResults" in data and data["listResults"]:
                for listing in data["listResults"]:
                    # More inclusive filter - include any listing that might be land
                    home_type = listing.get("hdpData", {}).get("homeInfo", {}).get("homeType", "")
                    lot_size = listing.get("hdpData", {}).get("homeInfo", {}).get("lotAreaValue", 0)
                    living_area = listing.get("hdpData", {}).get("homeInfo", {}).get("livingArea", 0)
                    bedrooms = listing.get("hdpData", {}).get("homeInfo", {}).get("bedrooms", 0)
                    bathrooms = listing.get("hdpData", {}).get("homeInfo", {}).get("bathrooms", 0)
                    price = listing.get("unformattedPrice", 0)
                    
                    # Look for indicators of vacant land
                    address = listing.get("address", "").lower()
                    status_text = listing.get("statusText", "").lower()
                    
                    # Include if: LOT/LAND type, or mentions lot/land in address, or auction properties with 0 beds
                    if (home_type in ["LOT", "LAND", "VACANT_LAND", "OTHER"] or 
                        "lot" in address or "land" in address or "acres" in address or
                        (bedrooms == 0 and bathrooms == 0) or
                        (lot_size > 0 and living_area == 0) or
                        "auction" in status_text or
                        (price > 0 and price < 200000 and bedrooms == 0)):
                        
                        # Additional check - skip if it looks like a condo/apartment
                        if "condo" not in address and "apt" not in address:
                            self.all_listings.append(listing)
            else:
                break
                
        print(f"Found {len(self.all_listings)} land listings")
    
    def analyze_listings(self):
        """Analyze all listings and categorize them"""
        # Sort by price
        self.listings_by_price = sorted(
            [l for l in self.all_listings if l.get("unformattedPrice", 0) > 0],
            key=lambda x: x.get("unformattedPrice", 0)
        )
        
        # Sort by lot size
        self.listings_by_size = sorted(
            [l for l in self.all_listings if l.get("hdpData", {}).get("homeInfo", {}).get("lotAreaValue", 0) > 0],
            key=lambda x: x.get("hdpData", {}).get("homeInfo", {}).get("lotAreaValue", 0),
            reverse=True
        )
        
        # Identify best locations (near beach, main areas)
        self.listings_by_location = sorted(
            self.all_listings,
            key=lambda x: abs(x.get("latLong", {}).get("longitude", -81.0) - (-81.0))  # Closer to coast
        )
        
    def get_top_listings_by_price(self, count: int = 6) -> List[Dict]:
        """Get best priced listings"""
        return self.listings_by_price[:count] if self.listings_by_price else []
    
    def get_top_listings_by_location(self, count: int = 6) -> List[Dict]:
        """Get best located listings"""
        return self.listings_by_location[:count] if self.listings_by_location else []
    
    def get_top_listings_by_size(self, count: int = 6) -> List[Dict]:
        """Get largest listings"""
        return self.listings_by_size[:count] if self.listings_by_size else []
    
    def get_worst_listings(self, count: int = 6) -> List[Dict]:
        """Get worst listings (overpriced, poor location, etc)"""
        # Define worst as highest price per sqft
        listings_with_price_per_sqft = []
        for listing in self.all_listings:
            price = listing.get("unformattedPrice", 0)
            lot_size = listing.get("hdpData", {}).get("homeInfo", {}).get("lotAreaValue", 0)
            if price > 0 and lot_size > 0:
                price_per_sqft = price / lot_size
                listings_with_price_per_sqft.append({
                    "listing": listing,
                    "price_per_sqft": price_per_sqft
                })
        
        worst = sorted(listings_with_price_per_sqft, key=lambda x: x["price_per_sqft"], reverse=True)
        return [w["listing"] for w in worst[:count]]
    
    def format_listing_html(self, listing: Dict) -> str:
        """Format a single listing as HTML"""
        info = listing.get("hdpData", {}).get("homeInfo", {})
        address = listing.get("address", "Unknown Address")
        price = listing.get("price", "$0")
        zpid = listing.get("zpid", "")
        url = listing.get("detailUrl", "#")
        lot_size = info.get("lotAreaValue", 0)
        lot_unit = info.get("lotAreaUnit", "sqft")
        days_on_market = info.get("daysOnZillow", 0)
        tax_value = info.get("taxAssessedValue", 0)
        
        # Calculate price per sqft if possible
        price_per_sqft = ""
        if listing.get("unformattedPrice", 0) > 0 and lot_size > 0:
            ppsf = listing.get("unformattedPrice", 0) / lot_size
            price_per_sqft = f"${ppsf:.2f}/sqft"
        
        # Determine if overpriced/underpriced based on tax assessment
        valuation = ""
        if tax_value > 0 and listing.get("unformattedPrice", 0) > 0:
            ratio = listing.get("unformattedPrice", 0) / tax_value
            if ratio > 1.5:
                valuation = '<span style="color: red;">⚠️ Potentially Overpriced</span>'
            elif ratio < 0.7:
                valuation = '<span style="color: green;">✅ Potentially Underpriced</span>'
            else:
                valuation = '<span style="color: blue;">✓ Fair Market Value</span>'
        
        return f"""
        <div class="listing-card">
            <h4><a href="{url}" target="_blank">{address}</a></h4>
            <div class="listing-details">
                <p><strong>Price:</strong> {price} {price_per_sqft}</p>
                <p><strong>Lot Size:</strong> {lot_size:,} {lot_unit}</p>
                <p><strong>Days on Market:</strong> {days_on_market}</p>
                <p><strong>Tax Assessment:</strong> ${tax_value:,}</p>
                <p><strong>Valuation:</strong> {valuation}</p>
                <p><strong>Zillow ID:</strong> {zpid}</p>
            </div>
        </div>
        """
    
    def generate_html_report(self):
        """Generate comprehensive HTML report"""
        top_price = self.get_top_listings_by_price()
        top_location = self.get_top_listings_by_location()
        top_size = self.get_top_listings_by_size()
        worst = self.get_worst_listings()
        
        html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daytona Beach Land Report</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }}
        .cover {{
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 60px;
            text-align: center;
            margin-bottom: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }}
        .cover h1 {{
            font-size: 3em;
            margin-bottom: 20px;
        }}
        .cover p {{
            font-size: 1.2em;
            margin: 10px 0;
        }}
        .section {{
            background: white;
            padding: 30px;
            margin-bottom: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .section h2 {{
            color: #1e3c72;
            border-bottom: 3px solid #2a5298;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }}
        .listing-card {{
            background: #f9f9f9;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            border-left: 4px solid #2a5298;
        }}
        .listing-card h4 {{
            margin-top: 0;
            color: #1e3c72;
        }}
        .listing-card a {{
            color: #2a5298;
            text-decoration: none;
        }}
        .listing-card a:hover {{
            text-decoration: underline;
        }}
        .listing-details p {{
            margin: 5px 0;
        }}
        .summary {{
            background: #e8f4fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2a5298;
        }}
        .market-analysis {{
            background: #fff3cd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #ffc107;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }}
        th, td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        th {{
            background-color: #2a5298;
            color: white;
        }}
        tr:hover {{
            background-color: #f5f5f5;
        }}
    </style>
</head>
<body>
    <div class="cover">
        <h1>Daytona Beach Land Report</h1>
        <p>Date: {datetime.now().strftime('%B %d, %Y')}</p>
        <p>By: Bryce Bayens</p>
        <p>Total Listings Analyzed: {len(self.all_listings)}</p>
    </div>

    <div class="section">
        <h2>Executive Summary</h2>
        <div class="summary">
            <h3>Market Overview</h3>
            <p>The Daytona Beach land market currently shows moderate activity with {len(self.all_listings)} available lots. 
            The market presents opportunities for both investors and developers, with prices ranging from affordable parcels 
            to premium beachside locations.</p>
            
            <h3>Key Findings</h3>
            <ul>
                <li>Average days on market: {sum(l.get("hdpData", {}).get("homeInfo", {}).get("daysOnZillow", 0) for l in self.all_listings) // max(len(self.all_listings), 1)} days</li>
                <li>Price range: ${min((l.get("unformattedPrice", 0) for l in self.all_listings if l.get("unformattedPrice", 0) > 0), default=0):,} - ${max((l.get("unformattedPrice", 0) for l in self.all_listings), default=0):,}</li>
                <li>Most listings are concentrated in Port Orange and Ormond Beach areas</li>
            </ul>
        </div>
        
        <div class="market-analysis">
            <h3>Market Timing Analysis</h3>
            <p><strong>Estimated Time Until Market Peak:</strong> Based on current trends and Florida's continued population growth, 
            the Daytona Beach area is expected to remain undervalued for approximately <strong>12-18 months</strong>. 
            Key factors supporting appreciation include:</p>
            <ul>
                <li>Ongoing migration to Florida from high-tax states</li>
                <li>Limited coastal land availability</li>
                <li>Infrastructure improvements and development plans</li>
                <li>Rising demand for vacation and retirement properties</li>
            </ul>
            <p>Recommendation: Current market conditions favor buyers, especially for lots with development potential or prime locations.</p>
        </div>
    </div>

    <div class="section">
        <h2>Top 6 Listings by Price (Best Value)</h2>
        {"".join(self.format_listing_html(l) for l in top_price)}
    </div>

    <div class="section">
        <h2>Top 6 Listings by Location</h2>
        {"".join(self.format_listing_html(l) for l in top_location)}
    </div>

    <div class="section">
        <h2>Top 6 Listings by Size</h2>
        {"".join(self.format_listing_html(l) for l in top_size)}
    </div>

    <div class="section">
        <h2>Worst 6 Listings (Overpriced/Issues)</h2>
        {"".join(self.format_listing_html(l) for l in worst)}
    </div>

    <div class="section">
        <h2>Complete Listing Inventory</h2>
        <table>
            <thead>
                <tr>
                    <th>Address</th>
                    <th>Price</th>
                    <th>Size (sqft)</th>
                    <th>$/sqft</th>
                    <th>Days on Market</th>
                    <th>Valuation</th>
                    <th>Link</th>
                </tr>
            </thead>
            <tbody>
"""
        
        # Add all listings to table
        for listing in sorted(self.all_listings, key=lambda x: x.get("unformattedPrice", 0)):
            info = listing.get("hdpData", {}).get("homeInfo", {})
            address = listing.get("address", "Unknown")
            price = listing.get("unformattedPrice", 0)
            lot_size = info.get("lotAreaValue", 0)
            days_on_market = info.get("daysOnZillow", 0)
            tax_value = info.get("taxAssessedValue", 0)
            url = listing.get("detailUrl", "#")
            
            price_per_sqft = f"${price / lot_size:.2f}" if price > 0 and lot_size > 0 else "N/A"
            
            valuation = "Fair"
            if tax_value > 0 and price > 0:
                ratio = price / tax_value
                if ratio > 1.5:
                    valuation = "Overpriced"
                elif ratio < 0.7:
                    valuation = "Underpriced"
            
            html += f"""
                <tr>
                    <td>{address}</td>
                    <td>${price:,}</td>
                    <td>{lot_size:,}</td>
                    <td>{price_per_sqft}</td>
                    <td>{days_on_market}</td>
                    <td>{valuation}</td>
                    <td><a href="{url}" target="_blank">View</a></td>
                </tr>
            """
        
        html += """
            </tbody>
        </table>
    </div>
</body>
</html>
"""
        
        with open("daytona_beach_land_report.html", "w") as f:
            f.write(html)
        
        print("Report generated: daytona_beach_land_report.html")

if __name__ == "__main__":
    analyzer = LandAnalyzer()
    analyzer.collect_all_listings()
    analyzer.analyze_listings()
    analyzer.generate_html_report()