package config

import (
	"os"
	"strings"
	"propbolt/utils"
)

// ProxyConfig holds the configuration for proxy rotation
type ProxyConfig struct {
	ProxyRotator *utils.ProxyRotator
}

// LoadProxyConfig loads proxy configuration from environment variables
func LoadProxyConfig() (*ProxyConfig, error) {
	// Get proxy URLs from environment variable (comma-separated)
	proxyURLsEnv := os.Getenv("PROXY_URLS")
	
	var proxyURLs []string
	if proxyURLsEnv != "" {
		proxyURLs = strings.Split(proxyURLsEnv, ",")
		// Trim whitespace from each URL
		for i, url := range proxyURLs {
			proxyURLs[i] = strings.TrimSpace(url)
		}
	}
	
	// Create proxy rotator
	rotator, err := utils.NewProxyRotator(proxyURLs)
	if err != nil {
		return nil, err
	}
	
	return &ProxyConfig{
		ProxyRotator: rotator,
	}, nil
}

// GetProxy returns the next proxy URL from the rotator
func (pc *ProxyConfig) GetProxy() *utils.ProxyRotator {
	return pc.ProxyRotator
}
