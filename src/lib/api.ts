import {
  Property,
  PropertySearchRequest,
  PropertySearchResponse,
  DashboardStats,
  SyncResponse,
} from '@/types/property';

// Use relative URLs to leverage Next.js proxy and avoid CORS issues
const API_BASE_URL = '';

class APIError extends Error {
  constructor(message: string, public status: number) {
    super(message);
    this.name = 'APIError';
  }
}

async function fetchAPI<T>(endpoint: string, options?: RequestInit): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new APIError(
        `API request failed: ${response.statusText}`,
        response.status
      );
    }

    return await response.json();
  } catch (error) {
    if (error instanceof APIError) {
      throw error;
    }
    throw new APIError(`Network error: ${error instanceof Error ? error.message : 'Unknown error'}`, 0);
  }
}

export const propertyAPI = {
  // Search properties with filters
  searchProperties: async (searchRequest: PropertySearchRequest): Promise<PropertySearchResponse> => {
    return fetchAPI<PropertySearchResponse>('/api/search', {
      method: 'POST',
      body: JSON.stringify(searchRequest),
    });
  },

  // Get dashboard statistics
  getDashboardStats: async (): Promise<DashboardStats> => {
    return fetchAPI<DashboardStats>('/api/dashboard/stats');
  },

  // Sync live property data
  syncProperties: async (): Promise<SyncResponse> => {
    return fetchAPI<SyncResponse>('/api/sync-properties', {
      method: 'POST',
    });
  },

  // Refresh all data
  refreshData: async (): Promise<SyncResponse> => {
    return fetchAPI<SyncResponse>('/api/refresh-data', {
      method: 'POST',
    });
  },

  // Get property details by ID
  getPropertyDetails: async (id: string): Promise<Property> => {
    return fetchAPI<Property>(`/property?id=${id}`);
  },

  // Get property details by address
  getPropertyByAddress: async (address: string): Promise<Property> => {
    return fetchAPI<Property>(`/property?address=${encodeURIComponent(address)}`);
  },

  // Check API status
  getStatus: async (): Promise<{ status: string; timestamp: string }> => {
    return fetchAPI<{ status: string; timestamp: string }>('/status');
  },
};

export { APIError };
