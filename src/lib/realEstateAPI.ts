import { RealEstateAPIProperty } from '@/types/property';

const REAL_ESTATE_API_KEY = process.env.NEXT_PUBLIC_REAL_ESTATE_API_KEY || 'AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914';
const REAL_ESTATE_API_V1_URL = 'https://api.realestateapi.com/v1';
const REAL_ESTATE_API_V2_URL = 'https://api.realestateapi.com/v2';

class RealEstateAPIError extends Error {
  constructor(message: string, public status: number) {
    super(message);
    this.name = 'RealEstateAPIError';
  }
}

// Generate a unique user identifier for API requests
const generateUserId = () => {
  return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

async function fetchRealEstateAPI<T>(
  endpoint: string,
  version: 'v1' | 'v2' = 'v2',
  params?: Record<string, string>,
  method: 'GET' | 'POST' = 'GET',
  body?: any
): Promise<T> {
  const baseUrl = version === 'v1' ? REAL_ESTATE_API_V1_URL : REAL_ESTATE_API_V2_URL;
  const url = new URL(`${baseUrl}${endpoint}`);

  // For GET requests, add params to URL
  if (method === 'GET' && params) {
    const searchParams = new URLSearchParams(params);
    url.search = searchParams.toString();
  }

  try {
    const requestOptions: RequestInit = {
      method,
      headers: {
        'x-api-key': REAL_ESTATE_API_KEY,
        'x-user-id': generateUserId(),
        'content-type': 'application/json',
        'Accept': 'application/json',
      },
    };

    // For POST requests, add body
    if (method === 'POST' && body) {
      requestOptions.body = JSON.stringify(body);
    }

    const response = await fetch(url.toString(), requestOptions);

    if (!response.ok) {
      throw new RealEstateAPIError(
        `Real Estate API request failed: ${response.statusText}`,
        response.status
      );
    }

    return await response.json();
  } catch (error) {
    if (error instanceof RealEstateAPIError) {
      throw error;
    }
    throw new RealEstateAPIError(
      `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      0
    );
  }
}

export const realEstateAPI = {
  // Get property parcel data by address using v1 API
  getPropertyParcel: async (address: string): Promise<RealEstateAPIProperty> => {
    return fetchRealEstateAPI<RealEstateAPIProperty>(
      '/PropertyParcel',
      'v1',
      { address: address }
    );
  },

  // Get property parcel data by coordinates using v1 API
  getPropertyParcelByCoords: async (lat: number, lng: number): Promise<RealEstateAPIProperty> => {
    return fetchRealEstateAPI<RealEstateAPIProperty>(
      '/PropertyParcel',
      'v1',
      {
        latitude: lat.toString(),
        longitude: lng.toString(),
      }
    );
  },

  // Get property boundaries in GeoJSON format using v1 API
  getPropertyBoundaries: async (parcelId: string): Promise<any> => {
    return fetchRealEstateAPI<any>(
      `/PropertyParcel/${parcelId}/boundaries`,
      'v1'
    );
  },

  // Get mapping pins using v2 API
  getPropertyMappingPins: async (
    neLat: number,
    neLng: number,
    swLat: number,
    swLng: number
  ): Promise<any> => {
    const requestBody = {
      boundingBox: {
        northEast: {
          latitude: neLat,
          longitude: neLng
        },
        southWest: {
          latitude: swLat,
          longitude: swLng
        }
      },
      propertyType: 'land',
      maxResults: 100
    };

    return fetchRealEstateAPI<any>(
      '/PropertyMapping',
      'v2',
      undefined,
      'POST',
      requestBody
    );
  },

  // Search properties in area using v1 API (fallback)
  searchPropertiesInArea: async (
    neLat: number,
    neLng: number,
    swLat: number,
    swLng: number
  ): Promise<RealEstateAPIProperty[]> => {
    return fetchRealEstateAPI<RealEstateAPIProperty[]>(
      '/property/search',
      'v1',
      {
        ne_lat: neLat.toString(),
        ne_lng: neLng.toString(),
        sw_lat: swLat.toString(),
        sw_lng: swLng.toString(),
        property_type: 'land',
      }
    );
  },

  // Get property details with boundaries in GeoJSON format
  getPropertyWithBoundaries: async (address: string): Promise<{
    property: RealEstateAPIProperty;
    boundaries: any;
  }> => {
    try {
      const property = await realEstateAPI.getPropertyParcel(address);
      let boundaries = null;

      // Try to get boundaries if parcel ID is available
      if (property && (property as any).parcelId) {
        try {
          boundaries = await realEstateAPI.getPropertyBoundaries((property as any).parcelId);
        } catch (error) {
          console.warn('Could not fetch property boundaries:', error);
        }
      }

      return {
        property,
        boundaries
      };
    } catch (error) {
      throw new RealEstateAPIError(
        `Failed to get property with boundaries: ${error instanceof Error ? error.message : 'Unknown error'}`,
        0
      );
    }
  },
};

export { RealEstateAPIError };
