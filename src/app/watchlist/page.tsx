'use client';

import React from 'react';
import { Heart, Plus } from 'lucide-react';

export default function WatchlistPage() {
  return (
    <div className="p-8 space-y-8">
      {/* Page Header */}
      <div className="card">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-zillow-dark-gray mb-2">
              Watch List
            </h1>
            <p className="text-gray-600">
              Keep track of your favorite vacant land properties
            </p>
          </div>
          
          <button className="btn-primary flex items-center space-x-2">
            <Plus size={16} />
            <span>Add Property</span>
          </button>
        </div>
      </div>

      {/* Empty State */}
      <div className="card text-center py-16">
        <Heart size={64} className="mx-auto text-gray-300 mb-4" />
        <h3 className="text-xl font-semibold text-gray-600 mb-2">
          No properties in your watchlist yet
        </h3>
        <p className="text-gray-500 mb-6">
          Start adding properties you&apos;re interested in to keep track of them
        </p>
        <button className="btn-primary">
          Browse Properties
        </button>
      </div>
    </div>
  );
}
