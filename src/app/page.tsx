'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { DashboardHeader } from '@/components/DashboardHeader';
import { StatsGrid } from '@/components/StatsGrid';
import { SearchSection } from '@/components/SearchSection';
import { MapAndResults } from '@/components/MapAndResults';
import { propertyAPI } from '@/lib/api';
import { useNotification } from '@/components/Providers';
import type { DashboardStats, Property, PropertySearchFilters } from '@/types/property';

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [properties, setProperties] = useState<Property[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<PropertySearchFilters>({});
  const { showNotification } = useNotification();

  const loadDashboardData = useCallback(async () => {
    try {
      setIsLoading(true);
      const [statsData, searchResults] = await Promise.all([
        propertyAPI.getDashboardStats(),
        propertyAPI.searchProperties({ query: '', filters: {} }),
      ]);
      
      setStats(statsData);
      setProperties(searchResults.results);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      showNotification('Failed to load dashboard data', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [showNotification]);

  // Load initial data
  useEffect(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  const handleSearch = async (query: string, searchFilters: PropertySearchFilters) => {
    try {
      setIsSearching(true);
      setSearchQuery(query);
      setFilters(searchFilters);
      
      const results = await propertyAPI.searchProperties({
        query,
        filters: searchFilters,
      });
      
      setProperties(results.results);
    } catch (error) {
      console.error('Error searching properties:', error);
      showNotification('Search failed. Please try again.', 'error');
    } finally {
      setIsSearching(false);
    }
  };

  const handleSyncData = async () => {
    try {
      const result = await propertyAPI.syncProperties();
      showNotification(`Successfully synced ${result.syncedCount} properties!`, 'success');
      
      // Reload dashboard data after sync
      await loadDashboardData();
    } catch (error) {
      console.error('Error syncing data:', error);
      showNotification('Failed to sync data. Please try again.', 'error');
    }
  };

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="flex items-center justify-center h-64">
          <div className="loading-spinner" />
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 space-y-8">
      <DashboardHeader />
      
      {stats && <StatsGrid stats={stats} />}
      
      <SearchSection
        onSearch={handleSearch}
        onSyncData={handleSyncData}
        isSearching={isSearching}
      />
      
      <MapAndResults
        properties={properties}
        searchQuery={searchQuery}
        filters={filters}
      />
    </div>
  );
}
