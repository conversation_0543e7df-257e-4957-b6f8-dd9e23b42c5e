'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { SearchSection } from '@/components/SearchSection';
import { MapAndResults } from '@/components/MapAndResults';
import { AdvancedFilters } from '@/components/AdvancedFilters';
import { propertyAPI } from '@/lib/api';
import { useNotification } from '@/components/Providers';
import type { Property, PropertySearchFilters } from '@/types/property';

export default function SearchPage() {
  const [properties, setProperties] = useState<Property[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<PropertySearchFilters>({});
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const { showNotification } = useNotification();

  const loadInitialProperties = useCallback(async () => {
    try {
      setIsLoading(true);
      const results = await propertyAPI.searchProperties({ query: '', filters: {} });
      setProperties(results.results);
    } catch (error) {
      console.error('Error loading properties:', error);
      showNotification('Failed to load properties', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [showNotification]);

  // Load initial properties
  useEffect(() => {
    loadInitialProperties();
  }, [loadInitialProperties]);

  const handleSearch = async (query: string, searchFilters: PropertySearchFilters) => {
    try {
      setIsSearching(true);
      setSearchQuery(query);
      setFilters(searchFilters);
      
      const results = await propertyAPI.searchProperties({
        query,
        filters: searchFilters,
      });
      
      setProperties(results.results);
      showNotification(`Found ${results.total} properties`, 'success');
    } catch (error) {
      console.error('Error searching properties:', error);
      showNotification('Search failed. Please try again.', 'error');
    } finally {
      setIsSearching(false);
    }
  };

  const handleSyncData = async () => {
    try {
      const result = await propertyAPI.syncProperties();
      showNotification(`Successfully synced ${result.syncedCount} properties!`, 'success');
      
      // Reload properties after sync
      await loadInitialProperties();
    } catch (error) {
      console.error('Error syncing data:', error);
      showNotification('Failed to sync data. Please try again.', 'error');
    }
  };

  const handleAdvancedFiltersApply = (advancedFilters: PropertySearchFilters) => {
    const combinedFilters = { ...filters, ...advancedFilters };
    handleSearch(searchQuery, combinedFilters);
    setShowAdvancedFilters(false);
  };

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="flex items-center justify-center h-64">
          <div className="loading-spinner" />
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 space-y-8">
      {/* Page Header */}
      <div className="card">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-zillow-dark-gray mb-2">
              Vacant Land Search
            </h1>
            <p className="text-gray-600">
              Find the perfect vacant land opportunities in Daytona Beach, FL
            </p>
          </div>
          
          <div className="mt-4 lg:mt-0">
            <button
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              className="btn-secondary"
            >
              {showAdvancedFilters ? 'Hide' : 'Show'} Advanced Filters
            </button>
          </div>
        </div>
      </div>

      {/* Search Section */}
      <SearchSection
        onSearch={handleSearch}
        onSyncData={handleSyncData}
        isSearching={isSearching}
      />

      {/* Advanced Filters */}
      {showAdvancedFilters && (
        <AdvancedFilters
          onApply={handleAdvancedFiltersApply}
          onClose={() => setShowAdvancedFilters(false)}
        />
      )}

      {/* Results Summary */}
      <div className="card">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-zillow-dark-gray">
              Search Results
            </h2>
            <p className="text-gray-600">
              {properties.length} properties found
              {searchQuery && ` for "${searchQuery}"`}
            </p>
          </div>
          
          {properties.length > 0 && (
            <div className="text-sm text-gray-500">
              Showing all results
            </div>
          )}
        </div>
      </div>

      {/* Map and Results */}
      <MapAndResults
        properties={properties}
        searchQuery={searchQuery}
        filters={filters}
      />
    </div>
  );
}
