(()=>{var e={};e.id=938,e.ids=[938],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4740:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>h,tree:()=>o}),a(5964),a(8567),a(5866);var t=a(3191),i=a(8716),r=a(7922),l=a.n(r),n=a(5231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let o=["",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,5964)),"/Users/<USER>/byte-media/v1-go/src/app/settings/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,8567)),"/Users/<USER>/byte-media/v1-go/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,5866,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/byte-media/v1-go/src/app/settings/page.tsx"],m="/settings/page",x={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},9412:(e,s,a)=>{Promise.resolve().then(a.t.bind(a,2994,23)),Promise.resolve().then(a.t.bind(a,6114,23)),Promise.resolve().then(a.t.bind(a,9727,23)),Promise.resolve().then(a.t.bind(a,9671,23)),Promise.resolve().then(a.t.bind(a,1868,23)),Promise.resolve().then(a.t.bind(a,4759,23))},4099:(e,s,a)=>{Promise.resolve().then(a.bind(a,8015)),Promise.resolve().then(a.bind(a,5596))},690:(e,s,a)=>{Promise.resolve().then(a.bind(a,7397))},7397:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>o});var t=a(326);a(7577);var i=a(6557);let r=(0,i.Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),l=(0,i.Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]),n=(0,i.Z)("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]]),c=(0,i.Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);function o(){return(0,t.jsxs)("div",{className:"p-8 space-y-8",children:[t.jsx("div",{className:"card",children:(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold text-zillow-dark-gray mb-2",children:"Settings"}),t.jsx("p",{className:"text-gray-600",children:"Manage your account and application preferences"})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[t.jsx(r,{size:20,className:"text-zillow-blue"}),t.jsx("h3",{className:"text-lg font-semibold",children:"Account Settings"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Display Name"}),t.jsx("input",{type:"text",defaultValue:"Admin User",className:"input-field"})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),t.jsx("input",{type:"email",defaultValue:"<EMAIL>",className:"input-field"})]}),t.jsx("button",{className:"btn-primary",children:"Save Changes"})]})]}),(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[t.jsx(l,{size:20,className:"text-zillow-blue"}),t.jsx("h3",{className:"text-lg font-semibold",children:"Notifications"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{children:"New property alerts"}),t.jsx("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{children:"Price change notifications"}),t.jsx("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{children:"Weekly market reports"}),t.jsx("input",{type:"checkbox",className:"rounded"})]})]})]}),(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[t.jsx(n,{size:20,className:"text-zillow-blue"}),t.jsx("h3",{className:"text-lg font-semibold",children:"API Configuration"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Mapbox Token"}),t.jsx("input",{type:"password",defaultValue:"pk.eyJ1Ijoic2F2ZXVuZGVyY29udHJhY3QiLCJhIjoiY202anNoNWtvMDRpMjJqb2x1MWZ3ZGYxbCJ9.Bxl18yPka0BUSrGibMTbhg",className:"input-field"})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Real Estate API Key"}),t.jsx("input",{type:"password",defaultValue:"AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914",className:"input-field"})]})]})]}),(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[t.jsx(c,{size:20,className:"text-zillow-blue"}),t.jsx("h3",{className:"text-lg font-semibold",children:"Database Settings"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{children:"Auto-sync properties"}),t.jsx("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{children:"Cache search results"}),t.jsx("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),t.jsx("button",{className:"btn-secondary",children:"Clear Cache"})]})]})]})]})}},8015:(e,s,a)=>{"use strict";a.d(s,{Navbar:()=>u});var t=a(326),i=a(7577),r=a(434),l=a(5047),n=a(6464),c=a(8307),o=a(7427),d=a(7069),m=a(6283),x=a(8378),h=a(4019),p=a(748);let b=[{href:"/",label:"Dashboard",icon:n.Z},{href:"/search",label:"Land Search",icon:c.Z},{href:"/watchlist",label:"Watch List",icon:o.Z},{href:"/analytics",label:"Market Analytics",icon:d.Z},{href:"/reports",label:"Reports",icon:m.Z},{href:"/settings",label:"Settings",icon:x.Z}];function u(){let e=(0,l.usePathname)(),[s,a]=(0,i.useState)(!1);return(0,t.jsxs)(t.Fragment,{children:[t.jsx("button",{onClick:()=>a(!s),className:"lg:hidden fixed top-4 left-4 z-50 p-2 bg-zillow-blue text-white rounded-lg shadow-lg",children:s?t.jsx(h.Z,{size:24}):t.jsx(p.Z,{size:24})}),s&&t.jsx("div",{className:"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40",onClick:()=>a(!1)}),(0,t.jsxs)("nav",{className:`
          fixed left-0 top-0 h-full w-80 bg-gradient-to-b from-zillow-blue to-zillow-blue-dark
          text-white z-40 shadow-zillow-lg transition-transform duration-300 ease-in-out
          ${s?"translate-x-0":"-translate-x-full lg:translate-x-0"}
        `,children:[(0,t.jsxs)("div",{className:"p-8 border-b border-white/10",children:[t.jsx("div",{className:"text-2xl font-bold mb-2",children:"Vacant Land Search"}),t.jsx("div",{className:"text-sm opacity-80",children:"Daytona Beach, FL"})]}),t.jsx("div",{className:"py-4",children:b.map(s=>{let i=s.icon,l=e===s.href;return(0,t.jsxs)(r.default,{href:s.href,className:`navbar-item ${l?"active":""}`,onClick:()=>a(!1),children:[t.jsx(i,{size:20,className:"mr-3"}),s.label]},s.href)})}),(0,t.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-6 border-t border-white/10",children:[t.jsx("div",{className:"text-xs opacity-60",children:"\xa9 2024 Vacant Land Search"}),t.jsx("div",{className:"text-xs opacity-60 mt-1",children:"Powered by Real Estate API"})]})]})]})}},5596:(e,s,a)=>{"use strict";a.d(s,{Providers:()=>n,l:()=>l});var t=a(326),i=a(7577);let r=(0,i.createContext)(void 0);function l(){let e=(0,i.useContext)(r);if(!e)throw Error("useNotification must be used within a NotificationProvider");return e}function n({children:e}){let[s,a]=(0,i.useState)([]),l=(0,i.useCallback)((e,s="info")=>{let t=Math.random().toString(36).substr(2,9),i={id:t,message:e,type:s};a(e=>[...e,i]),setTimeout(()=>{a(e=>e.filter(e=>e.id!==t))},5e3)},[]),n=(0,i.useCallback)(e=>{a(s=>s.filter(s=>s.id!==e))},[]);return(0,t.jsxs)(r.Provider,{value:{showNotification:l},children:[e,t.jsx("div",{className:"fixed top-5 right-5 z-50 space-y-2",children:s.map(e=>t.jsx("div",{className:`notification notification-${e.type} animate-slide-in`,onClick:()=>n(e.id),children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{children:e.message}),t.jsx("button",{onClick:()=>n(e.id),className:"ml-4 text-white hover:text-gray-200",children:"\xd7"})]})},e.id))})]})}},8567:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>d,metadata:()=>o});var t=a(9510),i=a(5317),r=a.n(i);a(5023);var l=a(8570);let n=(0,l.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx#Navbar`);(0,l.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx#useNotification`);let c=(0,l.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx#Providers`),o={title:"Vacant Land Search - Daytona Beach FL | Admin Dashboard",description:"Professional real estate admin panel for vacant land search in Daytona Beach, Florida. Find commercial and residential zoning opportunities with chain lease potential.",keywords:"vacant land, Daytona Beach, real estate, commercial zoning, residential zoning, chain lease, property search",authors:[{name:"Vacant Land Search Team"}],viewport:"width=device-width, initial-scale=1",robots:"index, follow",openGraph:{title:"Vacant Land Search - Daytona Beach FL",description:"Professional real estate admin panel for vacant land opportunities",type:"website",locale:"en_US"}};function d({children:e}){return(0,t.jsxs)("html",{lang:"en",children:[(0,t.jsxs)("head",{children:[t.jsx("link",{rel:"icon",href:"/favicon.ico"}),t.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),t.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:""}),t.jsx("script",{src:"https://api.mapbox.com/mapbox-gl-js/v3.0.1/mapbox-gl.js",async:!0}),t.jsx("link",{href:"https://api.mapbox.com/mapbox-gl-js/v3.0.1/mapbox-gl.css",rel:"stylesheet"})]}),t.jsx("body",{className:r().className,children:t.jsx(c,{children:(0,t.jsxs)("div",{className:"min-h-screen bg-zillow-gray",children:[t.jsx(n,{}),t.jsx("main",{className:"ml-0 lg:ml-80 min-h-screen",children:e})]})})})]})}},5964:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(8570).createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/app/settings/page.tsx#default`)},5023:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[415],()=>a(4740));module.exports=t})();