(()=>{var e={};e.id=931,e.ids=[931],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7053:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),r(5480),r(8567),r(5866);var t=r(3191),a=r(8716),l=r(7922),i=r.n(l),o=r(5231),n={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(s,n);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5480)),"/Users/<USER>/byte-media/v1-go/src/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,8567)),"/Users/<USER>/byte-media/v1-go/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/byte-media/v1-go/src/app/page.tsx"],u="/page",x={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},758:(e,s,r)=>{Promise.resolve().then(r.bind(r,5519))},5519:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>x});var t=r(326),a=r(7577),l=r(1223);function i(){let[e,s]=(0,a.useState)(new Date);return t.jsx("section",{className:"card",children:(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between",children:[(0,t.jsxs)("div",{className:"mb-4 lg:mb-0",children:[t.jsx("h1",{className:"text-3xl font-bold text-zillow-dark-gray mb-2",children:"Hello, Admin!"}),t.jsx("p",{className:"text-gray-600",children:"Welcome to your vacant land search dashboard"})]}),(0,t.jsxs)("div",{className:"text-right text-gray-600",children:[t.jsx("div",{className:"text-lg font-medium",children:(0,l.p6)(e)}),t.jsx("div",{className:"text-sm",children:(0,l.mr)(e)})]})]})})}function o({stats:e}){let s=[{label:"Total Properties",value:(0,l.uf)(e.totalProperties),color:"text-zillow-blue"},{label:"Average Price",value:(0,l.T4)(e.averagePrice),color:"text-zillow-blue"},{label:"New Listings (24h)",value:(0,l.uf)(e.newListings),color:"text-zillow-blue"},{label:"Sold This Month",value:(0,l.uf)(e.soldThisMonth),color:"text-zillow-blue"}];return t.jsx("section",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:s.map((e,s)=>(0,t.jsxs)("div",{className:"stat-card",children:[t.jsx("div",{className:`text-3xl font-bold mb-2 ${e.color}`,children:e.value}),t.jsx("div",{className:"text-gray-600 text-sm",children:e.label})]},s))})}var n=r(4582),c=r(27),d=r(8069),u=r(5596);function x(){let[e,s]=(0,a.useState)(null),[r,l]=(0,a.useState)([]),[x,p]=(0,a.useState)(!0),[m,g]=(0,a.useState)(!1),[h,y]=(0,a.useState)(""),[b,v]=(0,a.useState)({}),{showNotification:f}=(0,u.l)(),j=(0,a.useCallback)(async()=>{try{p(!0);let[e,r]=await Promise.all([d.G.getDashboardStats(),d.G.searchProperties({query:"",filters:{}})]);s(e),l(r.results)}catch(e){console.error("Error loading dashboard data:",e),f("Failed to load dashboard data","error")}finally{p(!1)}},[f]),P=async(e,s)=>{try{g(!0),y(e),v(s);let r=await d.G.searchProperties({query:e,filters:s});l(r.results)}catch(e){console.error("Error searching properties:",e),f("Search failed. Please try again.","error")}finally{g(!1)}},w=async()=>{try{let e=await d.G.syncProperties();f(`Successfully synced ${e.syncedCount} properties!`,"success"),await j()}catch(e){console.error("Error syncing data:",e),f("Failed to sync data. Please try again.","error")}};return x?t.jsx("div",{className:"p-8",children:t.jsx("div",{className:"flex items-center justify-center h-64",children:t.jsx("div",{className:"loading-spinner"})})}):(0,t.jsxs)("div",{className:"p-8 space-y-8",children:[t.jsx(i,{}),e&&t.jsx(o,{stats:e}),t.jsx(n.y,{onSearch:P,onSyncData:w,isSearching:m}),t.jsx(c.a,{properties:r,searchQuery:h,filters:b})]})}},5480:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(8570).createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/app/page.tsx#default`)}};var s=require("../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[415,398,147],()=>r(7053));module.exports=t})();