(()=>{var e={};e.id=533,e.ids=[533],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7476:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>h,pages:()=>d,routeModule:()=>x,tree:()=>c}),s(5049),s(8567),s(5866);var a=s(3191),r=s(8716),i=s(7922),n=s.n(i),l=s(5231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c=["",{children:["watchlist",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5049)),"/Users/<USER>/byte-media/v1-go/src/app/watchlist/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,8567)),"/Users/<USER>/byte-media/v1-go/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/byte-media/v1-go/src/app/watchlist/page.tsx"],h="/watchlist/page",m={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/watchlist/page",pathname:"/watchlist",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9412:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2994,23)),Promise.resolve().then(s.t.bind(s,6114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,9671,23)),Promise.resolve().then(s.t.bind(s,1868,23)),Promise.resolve().then(s.t.bind(s,4759,23))},4099:(e,t,s)=>{Promise.resolve().then(s.bind(s,8015)),Promise.resolve().then(s.bind(s,5596))},6171:(e,t,s)=>{Promise.resolve().then(s.bind(s,7014))},7014:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(326);s(7577);let r=(0,s(6557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var i=s(7427);function n(){return(0,a.jsxs)("div",{className:"p-8 space-y-8",children:[a.jsx("div",{className:"card",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-zillow-dark-gray mb-2",children:"Watch List"}),a.jsx("p",{className:"text-gray-600",children:"Keep track of your favorite vacant land properties"})]}),(0,a.jsxs)("button",{className:"btn-primary flex items-center space-x-2",children:[a.jsx(r,{size:16}),a.jsx("span",{children:"Add Property"})]})]})}),(0,a.jsxs)("div",{className:"card text-center py-16",children:[a.jsx(i.Z,{size:64,className:"mx-auto text-gray-300 mb-4"}),a.jsx("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"No properties in your watchlist yet"}),a.jsx("p",{className:"text-gray-500 mb-6",children:"Start adding properties you're interested in to keep track of them"}),a.jsx("button",{className:"btn-primary",children:"Browse Properties"})]})]})}},8015:(e,t,s)=>{"use strict";s.d(t,{Navbar:()=>u});var a=s(326),r=s(7577),i=s(434),n=s(5047),l=s(6464),o=s(8307),c=s(7427),d=s(7069),h=s(6283),m=s(8378),x=s(4019),p=s(748);let b=[{href:"/",label:"Dashboard",icon:l.Z},{href:"/search",label:"Land Search",icon:o.Z},{href:"/watchlist",label:"Watch List",icon:c.Z},{href:"/analytics",label:"Market Analytics",icon:d.Z},{href:"/reports",label:"Reports",icon:h.Z},{href:"/settings",label:"Settings",icon:m.Z}];function u(){let e=(0,n.usePathname)(),[t,s]=(0,r.useState)(!1);return(0,a.jsxs)(a.Fragment,{children:[a.jsx("button",{onClick:()=>s(!t),className:"lg:hidden fixed top-4 left-4 z-50 p-2 bg-zillow-blue text-white rounded-lg shadow-lg",children:t?a.jsx(x.Z,{size:24}):a.jsx(p.Z,{size:24})}),t&&a.jsx("div",{className:"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40",onClick:()=>s(!1)}),(0,a.jsxs)("nav",{className:`
          fixed left-0 top-0 h-full w-80 bg-gradient-to-b from-zillow-blue to-zillow-blue-dark
          text-white z-40 shadow-zillow-lg transition-transform duration-300 ease-in-out
          ${t?"translate-x-0":"-translate-x-full lg:translate-x-0"}
        `,children:[(0,a.jsxs)("div",{className:"p-8 border-b border-white/10",children:[a.jsx("div",{className:"text-2xl font-bold mb-2",children:"Vacant Land Search"}),a.jsx("div",{className:"text-sm opacity-80",children:"Daytona Beach, FL"})]}),a.jsx("div",{className:"py-4",children:b.map(t=>{let r=t.icon,n=e===t.href;return(0,a.jsxs)(i.default,{href:t.href,className:`navbar-item ${n?"active":""}`,onClick:()=>s(!1),children:[a.jsx(r,{size:20,className:"mr-3"}),t.label]},t.href)})}),(0,a.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-6 border-t border-white/10",children:[a.jsx("div",{className:"text-xs opacity-60",children:"\xa9 2024 Vacant Land Search"}),a.jsx("div",{className:"text-xs opacity-60 mt-1",children:"Powered by Real Estate API"})]})]})]})}},5596:(e,t,s)=>{"use strict";s.d(t,{Providers:()=>l,l:()=>n});var a=s(326),r=s(7577);let i=(0,r.createContext)(void 0);function n(){let e=(0,r.useContext)(i);if(!e)throw Error("useNotification must be used within a NotificationProvider");return e}function l({children:e}){let[t,s]=(0,r.useState)([]),n=(0,r.useCallback)((e,t="info")=>{let a=Math.random().toString(36).substr(2,9),r={id:a,message:e,type:t};s(e=>[...e,r]),setTimeout(()=>{s(e=>e.filter(e=>e.id!==a))},5e3)},[]),l=(0,r.useCallback)(e=>{s(t=>t.filter(t=>t.id!==e))},[]);return(0,a.jsxs)(i.Provider,{value:{showNotification:n},children:[e,a.jsx("div",{className:"fixed top-5 right-5 z-50 space-y-2",children:t.map(e=>a.jsx("div",{className:`notification notification-${e.type} animate-slide-in`,onClick:()=>l(e.id),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{children:e.message}),a.jsx("button",{onClick:()=>l(e.id),className:"ml-4 text-white hover:text-gray-200",children:"\xd7"})]})},e.id))})]})}},8567:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>c});var a=s(9510),r=s(5317),i=s.n(r);s(5023);var n=s(8570);let l=(0,n.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx#Navbar`);(0,n.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx#useNotification`);let o=(0,n.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx#Providers`),c={title:"Vacant Land Search - Daytona Beach FL | Admin Dashboard",description:"Professional real estate admin panel for vacant land search in Daytona Beach, Florida. Find commercial and residential zoning opportunities with chain lease potential.",keywords:"vacant land, Daytona Beach, real estate, commercial zoning, residential zoning, chain lease, property search",authors:[{name:"Vacant Land Search Team"}],viewport:"width=device-width, initial-scale=1",robots:"index, follow",openGraph:{title:"Vacant Land Search - Daytona Beach FL",description:"Professional real estate admin panel for vacant land opportunities",type:"website",locale:"en_US"}};function d({children:e}){return(0,a.jsxs)("html",{lang:"en",children:[(0,a.jsxs)("head",{children:[a.jsx("link",{rel:"icon",href:"/favicon.ico"}),a.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),a.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:""}),a.jsx("script",{src:"https://api.mapbox.com/mapbox-gl-js/v3.0.1/mapbox-gl.js",async:!0}),a.jsx("link",{href:"https://api.mapbox.com/mapbox-gl-js/v3.0.1/mapbox-gl.css",rel:"stylesheet"})]}),a.jsx("body",{className:i().className,children:a.jsx(o,{children:(0,a.jsxs)("div",{className:"min-h-screen bg-zillow-gray",children:[a.jsx(l,{}),a.jsx("main",{className:"ml-0 lg:ml-80 min-h-screen",children:e})]})})})]})}},5049:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(8570).createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/app/watchlist/page.tsx#default`)},5023:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[415],()=>s(7476));module.exports=a})();