(()=>{var e={};e.id=882,e.ids=[882],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3844:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>h,tree:()=>c}),t(4174),t(8567),t(5866);var a=t(3191),r=t(8716),i=t(7922),n=t.n(i),l=t(5231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let c=["",{children:["reports",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4174)),"/Users/<USER>/byte-media/v1-go/src/app/reports/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,8567)),"/Users/<USER>/byte-media/v1-go/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5866,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/byte-media/v1-go/src/app/reports/page.tsx"],x="/reports/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/reports/page",pathname:"/reports",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9412:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,2994,23)),Promise.resolve().then(t.t.bind(t,6114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,9671,23)),Promise.resolve().then(t.t.bind(t,1868,23)),Promise.resolve().then(t.t.bind(t,4759,23))},4099:(e,s,t)=>{Promise.resolve().then(t.bind(t,8015)),Promise.resolve().then(t.bind(t,5596))},1194:(e,s,t)=>{Promise.resolve().then(t.bind(t,2058))},1137:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},2058:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var a=t(326);t(7577);var r=t(6283),i=t(6557);let n=(0,i.Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),l=(0,i.Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]);var o=t(1137);function c(){return(0,a.jsxs)("div",{className:"p-8 space-y-8",children:[a.jsx("div",{className:"card",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-zillow-dark-gray mb-2",children:"Reports"}),a.jsx("p",{className:"text-gray-600",children:"Generate and download property reports"})]}),(0,a.jsxs)("button",{className:"btn-primary flex items-center space-x-2",children:[a.jsx(r.Z,{size:16}),a.jsx("span",{children:"Generate Report"})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"card-hover",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[a.jsx("div",{className:"p-2 bg-zillow-blue-light rounded-lg",children:a.jsx(r.Z,{size:20,className:"text-zillow-blue"})}),a.jsx("h3",{className:"text-lg font-semibold",children:"Property Summary"})]}),a.jsx("p",{className:"text-gray-600 mb-4",children:"Comprehensive overview of all vacant land properties"}),(0,a.jsxs)("button",{className:"btn-secondary w-full flex items-center justify-center space-x-2",children:[a.jsx(n,{size:16}),a.jsx("span",{children:"Download PDF"})]})]}),(0,a.jsxs)("div",{className:"card-hover",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[a.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:a.jsx(l,{size:20,className:"text-green-600"})}),a.jsx("h3",{className:"text-lg font-semibold",children:"Monthly Report"})]}),a.jsx("p",{className:"text-gray-600 mb-4",children:"Monthly market activity and trends analysis"}),(0,a.jsxs)("button",{className:"btn-secondary w-full flex items-center justify-center space-x-2",children:[a.jsx(n,{size:16}),a.jsx("span",{children:"Download PDF"})]})]}),(0,a.jsxs)("div",{className:"card-hover",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[a.jsx("div",{className:"p-2 bg-purple-100 rounded-lg",children:a.jsx(o.Z,{size:20,className:"text-purple-600"})}),a.jsx("h3",{className:"text-lg font-semibold",children:"Custom Report"})]}),a.jsx("p",{className:"text-gray-600 mb-4",children:"Create custom reports with specific filters"}),(0,a.jsxs)("button",{className:"btn-secondary w-full flex items-center justify-center space-x-2",children:[a.jsx(r.Z,{size:16}),a.jsx("span",{children:"Configure"})]})]})]}),(0,a.jsxs)("div",{className:"card",children:[a.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Recent Reports"}),(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[a.jsx(r.Z,{size:48,className:"mx-auto mb-4 text-gray-300"}),a.jsx("p",{children:"No reports generated yet"})]})]})]})}},8015:(e,s,t)=>{"use strict";t.d(s,{Navbar:()=>u});var a=t(326),r=t(7577),i=t(434),n=t(5047),l=t(6464),o=t(8307),c=t(7427),d=t(7069),x=t(6283),m=t(8378),h=t(4019),p=t(748);let b=[{href:"/",label:"Dashboard",icon:l.Z},{href:"/search",label:"Land Search",icon:o.Z},{href:"/watchlist",label:"Watch List",icon:c.Z},{href:"/analytics",label:"Market Analytics",icon:d.Z},{href:"/reports",label:"Reports",icon:x.Z},{href:"/settings",label:"Settings",icon:m.Z}];function u(){let e=(0,n.usePathname)(),[s,t]=(0,r.useState)(!1);return(0,a.jsxs)(a.Fragment,{children:[a.jsx("button",{onClick:()=>t(!s),className:"lg:hidden fixed top-4 left-4 z-50 p-2 bg-zillow-blue text-white rounded-lg shadow-lg",children:s?a.jsx(h.Z,{size:24}):a.jsx(p.Z,{size:24})}),s&&a.jsx("div",{className:"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40",onClick:()=>t(!1)}),(0,a.jsxs)("nav",{className:`
          fixed left-0 top-0 h-full w-80 bg-gradient-to-b from-zillow-blue to-zillow-blue-dark
          text-white z-40 shadow-zillow-lg transition-transform duration-300 ease-in-out
          ${s?"translate-x-0":"-translate-x-full lg:translate-x-0"}
        `,children:[(0,a.jsxs)("div",{className:"p-8 border-b border-white/10",children:[a.jsx("div",{className:"text-2xl font-bold mb-2",children:"Vacant Land Search"}),a.jsx("div",{className:"text-sm opacity-80",children:"Daytona Beach, FL"})]}),a.jsx("div",{className:"py-4",children:b.map(s=>{let r=s.icon,n=e===s.href;return(0,a.jsxs)(i.default,{href:s.href,className:`navbar-item ${n?"active":""}`,onClick:()=>t(!1),children:[a.jsx(r,{size:20,className:"mr-3"}),s.label]},s.href)})}),(0,a.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-6 border-t border-white/10",children:[a.jsx("div",{className:"text-xs opacity-60",children:"\xa9 2024 Vacant Land Search"}),a.jsx("div",{className:"text-xs opacity-60 mt-1",children:"Powered by Real Estate API"})]})]})]})}},5596:(e,s,t)=>{"use strict";t.d(s,{Providers:()=>l,l:()=>n});var a=t(326),r=t(7577);let i=(0,r.createContext)(void 0);function n(){let e=(0,r.useContext)(i);if(!e)throw Error("useNotification must be used within a NotificationProvider");return e}function l({children:e}){let[s,t]=(0,r.useState)([]),n=(0,r.useCallback)((e,s="info")=>{let a=Math.random().toString(36).substr(2,9),r={id:a,message:e,type:s};t(e=>[...e,r]),setTimeout(()=>{t(e=>e.filter(e=>e.id!==a))},5e3)},[]),l=(0,r.useCallback)(e=>{t(s=>s.filter(s=>s.id!==e))},[]);return(0,a.jsxs)(i.Provider,{value:{showNotification:n},children:[e,a.jsx("div",{className:"fixed top-5 right-5 z-50 space-y-2",children:s.map(e=>a.jsx("div",{className:`notification notification-${e.type} animate-slide-in`,onClick:()=>l(e.id),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{children:e.message}),a.jsx("button",{onClick:()=>l(e.id),className:"ml-4 text-white hover:text-gray-200",children:"\xd7"})]})},e.id))})]})}},8567:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d,metadata:()=>c});var a=t(9510),r=t(5317),i=t.n(r);t(5023);var n=t(8570);let l=(0,n.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx#Navbar`);(0,n.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx#useNotification`);let o=(0,n.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx#Providers`),c={title:"Vacant Land Search - Daytona Beach FL | Admin Dashboard",description:"Professional real estate admin panel for vacant land search in Daytona Beach, Florida. Find commercial and residential zoning opportunities with chain lease potential.",keywords:"vacant land, Daytona Beach, real estate, commercial zoning, residential zoning, chain lease, property search",authors:[{name:"Vacant Land Search Team"}],viewport:"width=device-width, initial-scale=1",robots:"index, follow",openGraph:{title:"Vacant Land Search - Daytona Beach FL",description:"Professional real estate admin panel for vacant land opportunities",type:"website",locale:"en_US"}};function d({children:e}){return(0,a.jsxs)("html",{lang:"en",children:[(0,a.jsxs)("head",{children:[a.jsx("link",{rel:"icon",href:"/favicon.ico"}),a.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),a.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:""}),a.jsx("script",{src:"https://api.mapbox.com/mapbox-gl-js/v3.0.1/mapbox-gl.js",async:!0}),a.jsx("link",{href:"https://api.mapbox.com/mapbox-gl-js/v3.0.1/mapbox-gl.css",rel:"stylesheet"})]}),a.jsx("body",{className:i().className,children:a.jsx(o,{children:(0,a.jsxs)("div",{className:"min-h-screen bg-zillow-gray",children:[a.jsx(l,{}),a.jsx("main",{className:"ml-0 lg:ml-80 min-h-screen",children:e})]})})})]})}},4174:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(8570).createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/app/reports/page.tsx#default`)},5023:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[415],()=>t(3844));module.exports=a})();