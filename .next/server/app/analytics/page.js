(()=>{var e={};e.id=109,e.ids=[109],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2444:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>h,pages:()=>d,routeModule:()=>x,tree:()=>o}),a(2388),a(8567),a(5866);var t=a(3191),i=a(8716),r=a(7922),n=a.n(r),l=a(5231),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);a.d(s,c);let o=["",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,2388)),"/Users/<USER>/byte-media/v1-go/src/app/analytics/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,8567)),"/Users/<USER>/byte-media/v1-go/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,5866,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/byte-media/v1-go/src/app/analytics/page.tsx"],h="/analytics/page",m={require:a,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/analytics/page",pathname:"/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},9412:(e,s,a)=>{Promise.resolve().then(a.t.bind(a,2994,23)),Promise.resolve().then(a.t.bind(a,6114,23)),Promise.resolve().then(a.t.bind(a,9727,23)),Promise.resolve().then(a.t.bind(a,9671,23)),Promise.resolve().then(a.t.bind(a,1868,23)),Promise.resolve().then(a.t.bind(a,4759,23))},4099:(e,s,a)=>{Promise.resolve().then(a.bind(a,8015)),Promise.resolve().then(a.bind(a,5596))},5799:(e,s,a)=>{Promise.resolve().then(a.bind(a,6682))},6682:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>o});var t=a(326);a(7577);var i=a(7069),r=a(6557);let n=(0,r.Z)("PieChart",[["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}],["path",{d:"M22 12A10 10 0 0 0 12 2v10z",key:"1rfc4y"}]]),l=(0,r.Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]);var c=a(6464);function o(){return(0,t.jsxs)("div",{className:"p-8 space-y-8",children:[t.jsx("div",{className:"card",children:(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold text-zillow-dark-gray mb-2",children:"Market Analytics"}),t.jsx("p",{className:"text-gray-600",children:"Analyze vacant land market trends in Daytona Beach, FL"})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[t.jsx(i.Z,{size:20,className:"text-zillow-blue"}),t.jsx("h3",{className:"text-lg font-semibold",children:"Price Trends"})]}),t.jsx("div",{className:"h-64 bg-gray-100 rounded-lg flex items-center justify-center",children:t.jsx("p",{className:"text-gray-500",children:"Price trend chart coming soon"})})]}),(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[t.jsx(n,{size:20,className:"text-zillow-blue"}),t.jsx("h3",{className:"text-lg font-semibold",children:"Zoning Distribution"})]}),t.jsx("div",{className:"h-64 bg-gray-100 rounded-lg flex items-center justify-center",children:t.jsx("p",{className:"text-gray-500",children:"Zoning chart coming soon"})})]}),(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[t.jsx(l,{size:20,className:"text-zillow-blue"}),t.jsx("h3",{className:"text-lg font-semibold",children:"Market Activity"})]}),t.jsx("div",{className:"h-64 bg-gray-100 rounded-lg flex items-center justify-center",children:t.jsx("p",{className:"text-gray-500",children:"Activity chart coming soon"})})]}),(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[t.jsx(c.Z,{size:20,className:"text-zillow-blue"}),t.jsx("h3",{className:"text-lg font-semibold",children:"Chain Potential Analysis"})]}),t.jsx("div",{className:"h-64 bg-gray-100 rounded-lg flex items-center justify-center",children:t.jsx("p",{className:"text-gray-500",children:"Chain potential chart coming soon"})})]})]})]})}},8015:(e,s,a)=>{"use strict";a.d(s,{Navbar:()=>g});var t=a(326),i=a(7577),r=a(434),n=a(5047),l=a(6464),c=a(8307),o=a(7427),d=a(7069),h=a(6283),m=a(8378),x=a(4019),p=a(748);let b=[{href:"/",label:"Dashboard",icon:l.Z},{href:"/search",label:"Land Search",icon:c.Z},{href:"/watchlist",label:"Watch List",icon:o.Z},{href:"/analytics",label:"Market Analytics",icon:d.Z},{href:"/reports",label:"Reports",icon:h.Z},{href:"/settings",label:"Settings",icon:m.Z}];function g(){let e=(0,n.usePathname)(),[s,a]=(0,i.useState)(!1);return(0,t.jsxs)(t.Fragment,{children:[t.jsx("button",{onClick:()=>a(!s),className:"lg:hidden fixed top-4 left-4 z-50 p-2 bg-zillow-blue text-white rounded-lg shadow-lg",children:s?t.jsx(x.Z,{size:24}):t.jsx(p.Z,{size:24})}),s&&t.jsx("div",{className:"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40",onClick:()=>a(!1)}),(0,t.jsxs)("nav",{className:`
          fixed left-0 top-0 h-full w-80 bg-gradient-to-b from-zillow-blue to-zillow-blue-dark
          text-white z-40 shadow-zillow-lg transition-transform duration-300 ease-in-out
          ${s?"translate-x-0":"-translate-x-full lg:translate-x-0"}
        `,children:[(0,t.jsxs)("div",{className:"p-8 border-b border-white/10",children:[t.jsx("div",{className:"text-2xl font-bold mb-2",children:"Vacant Land Search"}),t.jsx("div",{className:"text-sm opacity-80",children:"Daytona Beach, FL"})]}),t.jsx("div",{className:"py-4",children:b.map(s=>{let i=s.icon,n=e===s.href;return(0,t.jsxs)(r.default,{href:s.href,className:`navbar-item ${n?"active":""}`,onClick:()=>a(!1),children:[t.jsx(i,{size:20,className:"mr-3"}),s.label]},s.href)})}),(0,t.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-6 border-t border-white/10",children:[t.jsx("div",{className:"text-xs opacity-60",children:"\xa9 2024 Vacant Land Search"}),t.jsx("div",{className:"text-xs opacity-60 mt-1",children:"Powered by Real Estate API"})]})]})]})}},5596:(e,s,a)=>{"use strict";a.d(s,{Providers:()=>l,l:()=>n});var t=a(326),i=a(7577);let r=(0,i.createContext)(void 0);function n(){let e=(0,i.useContext)(r);if(!e)throw Error("useNotification must be used within a NotificationProvider");return e}function l({children:e}){let[s,a]=(0,i.useState)([]),n=(0,i.useCallback)((e,s="info")=>{let t=Math.random().toString(36).substr(2,9),i={id:t,message:e,type:s};a(e=>[...e,i]),setTimeout(()=>{a(e=>e.filter(e=>e.id!==t))},5e3)},[]),l=(0,i.useCallback)(e=>{a(s=>s.filter(s=>s.id!==e))},[]);return(0,t.jsxs)(r.Provider,{value:{showNotification:n},children:[e,t.jsx("div",{className:"fixed top-5 right-5 z-50 space-y-2",children:s.map(e=>t.jsx("div",{className:`notification notification-${e.type} animate-slide-in`,onClick:()=>l(e.id),children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{children:e.message}),t.jsx("button",{onClick:()=>l(e.id),className:"ml-4 text-white hover:text-gray-200",children:"\xd7"})]})},e.id))})]})}},2388:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(8570).createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/app/analytics/page.tsx#default`)},8567:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>d,metadata:()=>o});var t=a(9510),i=a(5317),r=a.n(i);a(5023);var n=a(8570);let l=(0,n.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx#Navbar`);(0,n.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx#useNotification`);let c=(0,n.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx#Providers`),o={title:"Vacant Land Search - Daytona Beach FL | Admin Dashboard",description:"Professional real estate admin panel for vacant land search in Daytona Beach, Florida. Find commercial and residential zoning opportunities with chain lease potential.",keywords:"vacant land, Daytona Beach, real estate, commercial zoning, residential zoning, chain lease, property search",authors:[{name:"Vacant Land Search Team"}],viewport:"width=device-width, initial-scale=1",robots:"index, follow",openGraph:{title:"Vacant Land Search - Daytona Beach FL",description:"Professional real estate admin panel for vacant land opportunities",type:"website",locale:"en_US"}};function d({children:e}){return(0,t.jsxs)("html",{lang:"en",children:[(0,t.jsxs)("head",{children:[t.jsx("link",{rel:"icon",href:"/favicon.ico"}),t.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),t.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:""}),t.jsx("script",{src:"https://api.mapbox.com/mapbox-gl-js/v3.0.1/mapbox-gl.js",async:!0}),t.jsx("link",{href:"https://api.mapbox.com/mapbox-gl-js/v3.0.1/mapbox-gl.css",rel:"stylesheet"})]}),t.jsx("body",{className:r().className,children:t.jsx(c,{children:(0,t.jsxs)("div",{className:"min-h-screen bg-zillow-gray",children:[t.jsx(l,{}),t.jsx("main",{className:"ml-0 lg:ml-80 min-h-screen",children:e})]})})})]})}},5023:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[415],()=>a(2444));module.exports=t})();