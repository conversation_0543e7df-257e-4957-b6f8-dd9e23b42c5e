(()=>{var e={};e.id=409,e.ids=[409],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4334:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>d,routeModule:()=>f,tree:()=>c}),n(7352),n(5866),n(8567);var a=n(3191),r=n(8716),s=n(7922),i=n.n(s),o=n(5231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);n.d(t,l);let c=["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.t.bind(n,5866,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,8567)),"/Users/<USER>/byte-media/v1-go/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,5866,23)),"next/dist/client/components/not-found-error"]}],d=[],u="/_not-found/page",h={require:n,loadChunk:()=>Promise.resolve()},f=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9412:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,2994,23)),Promise.resolve().then(n.t.bind(n,6114,23)),Promise.resolve().then(n.t.bind(n,9727,23)),Promise.resolve().then(n.t.bind(n,9671,23)),Promise.resolve().then(n.t.bind(n,1868,23)),Promise.resolve().then(n.t.bind(n,4759,23))},4099:(e,t,n)=>{Promise.resolve().then(n.bind(n,8015)),Promise.resolve().then(n.bind(n,5596))},8015:(e,t,n)=>{"use strict";n.d(t,{Navbar:()=>x});var a=n(326),r=n(7577),s=n(434),i=n(5047),o=n(6464),l=n(8307),c=n(7427),d=n(7069),u=n(6283),h=n(8378),f=n(4019),m=n(748);let p=[{href:"/",label:"Dashboard",icon:o.Z},{href:"/search",label:"Land Search",icon:l.Z},{href:"/watchlist",label:"Watch List",icon:c.Z},{href:"/analytics",label:"Market Analytics",icon:d.Z},{href:"/reports",label:"Reports",icon:u.Z},{href:"/settings",label:"Settings",icon:h.Z}];function x(){let e=(0,i.usePathname)(),[t,n]=(0,r.useState)(!1);return(0,a.jsxs)(a.Fragment,{children:[a.jsx("button",{onClick:()=>n(!t),className:"lg:hidden fixed top-4 left-4 z-50 p-2 bg-zillow-blue text-white rounded-lg shadow-lg",children:t?a.jsx(f.Z,{size:24}):a.jsx(m.Z,{size:24})}),t&&a.jsx("div",{className:"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40",onClick:()=>n(!1)}),(0,a.jsxs)("nav",{className:`
          fixed left-0 top-0 h-full w-80 bg-gradient-to-b from-zillow-blue to-zillow-blue-dark
          text-white z-40 shadow-zillow-lg transition-transform duration-300 ease-in-out
          ${t?"translate-x-0":"-translate-x-full lg:translate-x-0"}
        `,children:[(0,a.jsxs)("div",{className:"p-8 border-b border-white/10",children:[a.jsx("div",{className:"text-2xl font-bold mb-2",children:"Vacant Land Search"}),a.jsx("div",{className:"text-sm opacity-80",children:"Daytona Beach, FL"})]}),a.jsx("div",{className:"py-4",children:p.map(t=>{let r=t.icon,i=e===t.href;return(0,a.jsxs)(s.default,{href:t.href,className:`navbar-item ${i?"active":""}`,onClick:()=>n(!1),children:[a.jsx(r,{size:20,className:"mr-3"}),t.label]},t.href)})}),(0,a.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-6 border-t border-white/10",children:[a.jsx("div",{className:"text-xs opacity-60",children:"\xa9 2024 Vacant Land Search"}),a.jsx("div",{className:"text-xs opacity-60 mt-1",children:"Powered by Real Estate API"})]})]})]})}},5596:(e,t,n)=>{"use strict";n.d(t,{Providers:()=>o,l:()=>i});var a=n(326),r=n(7577);let s=(0,r.createContext)(void 0);function i(){let e=(0,r.useContext)(s);if(!e)throw Error("useNotification must be used within a NotificationProvider");return e}function o({children:e}){let[t,n]=(0,r.useState)([]),i=(0,r.useCallback)((e,t="info")=>{let a=Math.random().toString(36).substr(2,9),r={id:a,message:e,type:t};n(e=>[...e,r]),setTimeout(()=>{n(e=>e.filter(e=>e.id!==a))},5e3)},[]),o=(0,r.useCallback)(e=>{n(t=>t.filter(t=>t.id!==e))},[]);return(0,a.jsxs)(s.Provider,{value:{showNotification:i},children:[e,a.jsx("div",{className:"fixed top-5 right-5 z-50 space-y-2",children:t.map(e=>a.jsx("div",{className:`notification notification-${e.type} animate-slide-in`,onClick:()=>o(e.id),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{children:e.message}),a.jsx("button",{onClick:()=>o(e.id),className:"ml-4 text-white hover:text-gray-200",children:"\xd7"})]})},e.id))})]})}},6399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{isNotFoundError:function(){return r},notFound:function(){return a}});let n="NEXT_NOT_FOUND";function a(){let e=Error(n);throw e.digest=n,e}function r(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===n}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7352:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{PARALLEL_ROUTE_DEFAULT_PATH:function(){return r},default:function(){return s}});let a=n(6399),r="next/dist/client/components/parallel-route-default.js";function s(){(0,a.notFound)()}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8567:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>d,metadata:()=>c});var a=n(9510),r=n(5317),s=n.n(r);n(5023);var i=n(8570);let o=(0,i.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx#Navbar`);(0,i.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx#useNotification`);let l=(0,i.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx#Providers`),c={title:"Vacant Land Search - Daytona Beach FL | Admin Dashboard",description:"Professional real estate admin panel for vacant land search in Daytona Beach, Florida. Find commercial and residential zoning opportunities with chain lease potential.",keywords:"vacant land, Daytona Beach, real estate, commercial zoning, residential zoning, chain lease, property search",authors:[{name:"Vacant Land Search Team"}],viewport:"width=device-width, initial-scale=1",robots:"index, follow",openGraph:{title:"Vacant Land Search - Daytona Beach FL",description:"Professional real estate admin panel for vacant land opportunities",type:"website",locale:"en_US"}};function d({children:e}){return(0,a.jsxs)("html",{lang:"en",children:[(0,a.jsxs)("head",{children:[a.jsx("link",{rel:"icon",href:"/favicon.ico"}),a.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),a.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:""}),a.jsx("script",{src:"https://api.mapbox.com/mapbox-gl-js/v3.0.1/mapbox-gl.js",async:!0}),a.jsx("link",{href:"https://api.mapbox.com/mapbox-gl-js/v3.0.1/mapbox-gl.css",rel:"stylesheet"})]}),a.jsx("body",{className:s().className,children:a.jsx(l,{children:(0,a.jsxs)("div",{className:"min-h-screen bg-zillow-gray",children:[a.jsx(o,{}),a.jsx("main",{className:"ml-0 lg:ml-80 min-h-screen",children:e})]})})})]})}},5023:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),a=t.X(0,[415],()=>n(4334));module.exports=a})();