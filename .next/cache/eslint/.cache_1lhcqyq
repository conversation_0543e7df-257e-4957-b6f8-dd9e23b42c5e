[{"/Users/<USER>/byte-media/v1-go/src/app/analytics/page.tsx": "1", "/Users/<USER>/byte-media/v1-go/src/app/layout.tsx": "2", "/Users/<USER>/byte-media/v1-go/src/app/page.tsx": "3", "/Users/<USER>/byte-media/v1-go/src/app/reports/page.tsx": "4", "/Users/<USER>/byte-media/v1-go/src/app/search/page.tsx": "5", "/Users/<USER>/byte-media/v1-go/src/app/settings/page.tsx": "6", "/Users/<USER>/byte-media/v1-go/src/app/watchlist/page.tsx": "7", "/Users/<USER>/byte-media/v1-go/src/components/AdvancedFilters.tsx": "8", "/Users/<USER>/byte-media/v1-go/src/components/DashboardHeader.tsx": "9", "/Users/<USER>/byte-media/v1-go/src/components/MapAndResults.tsx": "10", "/Users/<USER>/byte-media/v1-go/src/components/MapComponent.tsx": "11", "/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx": "12", "/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx": "13", "/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx": "14", "/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx": "15", "/Users/<USER>/byte-media/v1-go/src/components/StatsGrid.tsx": "16", "/Users/<USER>/byte-media/v1-go/src/lib/api.ts": "17", "/Users/<USER>/byte-media/v1-go/src/lib/realEstateAPI.ts": "18", "/Users/<USER>/byte-media/v1-go/src/lib/utils.ts": "19", "/Users/<USER>/byte-media/v1-go/src/types/property.ts": "20"}, {"size": 2576, "mtime": 1749406242508, "results": "21", "hashOfConfig": "22"}, {"size": 1900, "mtime": 1749319888168, "results": "23", "hashOfConfig": "22"}, {"size": 3252, "mtime": 1749320414674, "results": "24", "hashOfConfig": "22"}, {"size": 3199, "mtime": 1749320090094, "results": "25", "hashOfConfig": "22"}, {"size": 4964, "mtime": 1749320375877, "results": "26", "hashOfConfig": "22"}, {"size": 4600, "mtime": 1749320105864, "results": "27", "hashOfConfig": "22"}, {"size": 1288, "mtime": 1749320385616, "results": "28", "hashOfConfig": "22"}, {"size": 5512, "mtime": 1749320058392, "results": "29", "hashOfConfig": "22"}, {"size": 1090, "mtime": 1749319940933, "results": "30", "hashOfConfig": "22"}, {"size": 1816, "mtime": 1749319995192, "results": "31", "hashOfConfig": "22"}, {"size": 5238, "mtime": 1749320014302, "results": "32", "hashOfConfig": "22"}, {"size": 2937, "mtime": 1749319908620, "results": "33", "hashOfConfig": "22"}, {"size": 2477, "mtime": 1749320025196, "results": "34", "hashOfConfig": "22"}, {"size": 2213, "mtime": 1749319896980, "results": "35", "hashOfConfig": "22"}, {"size": 5673, "mtime": 1749319986451, "results": "36", "hashOfConfig": "22"}, {"size": 1225, "mtime": 1749319969444, "results": "37", "hashOfConfig": "22"}, {"size": 2410, "mtime": 1749341795120, "results": "38", "hashOfConfig": "22"}, {"size": 4924, "mtime": 1749346537597, "results": "39", "hashOfConfig": "22"}, {"size": 3546, "mtime": 1749319865305, "results": "40", "hashOfConfig": "22"}, {"size": 1382, "mtime": 1749319829303, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "k1e1pg", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/byte-media/v1-go/src/app/analytics/page.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/app/layout.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/app/page.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/app/reports/page.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/app/search/page.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/app/settings/page.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/app/watchlist/page.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/components/AdvancedFilters.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/components/DashboardHeader.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/components/MapAndResults.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/components/MapComponent.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/components/StatsGrid.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/lib/api.ts", [], [], "/Users/<USER>/byte-media/v1-go/src/lib/realEstateAPI.ts", [], [], "/Users/<USER>/byte-media/v1-go/src/lib/utils.ts", [], [], "/Users/<USER>/byte-media/v1-go/src/types/property.ts", [], []]