(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[533],{9772:function(e,t,r){Promise.resolve().then(r.bind(r,2907))},9763:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var s=r(2265),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),i=(e,t)=>{let r=(0,s.forwardRef)((r,i)=>{let{color:c="currentColor",size:l=24,strokeWidth:o=2,absoluteStrokeWidth:d,className:u="",children:h,...m}=r;return(0,s.createElement)("svg",{ref:i,...n,width:l,height:l,stroke:c,strokeWidth:d?24*Number(o)/Number(l):o,className:["lucide","lucide-".concat(a(e)),u].join(" "),...m},[...t.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(h)?h:[h]])});return r.displayName="".concat(e),r}},8997:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(9763).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2907:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return i}});var s=r(7437);r(2265);let n=(0,r(9763).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var a=r(8997);function i(){return(0,s.jsxs)("div",{className:"p-8 space-y-8",children:[(0,s.jsx)("div",{className:"card",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-zillow-dark-gray mb-2",children:"Watch List"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Keep track of your favorite vacant land properties"})]}),(0,s.jsxs)("button",{className:"btn-primary flex items-center space-x-2",children:[(0,s.jsx)(n,{size:16}),(0,s.jsx)("span",{children:"Add Property"})]})]})}),(0,s.jsxs)("div",{className:"card text-center py-16",children:[(0,s.jsx)(a.Z,{size:64,className:"mx-auto text-gray-300 mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"No properties in your watchlist yet"}),(0,s.jsx)("p",{className:"text-gray-500 mb-6",children:"Start adding properties you're interested in to keep track of them"}),(0,s.jsx)("button",{className:"btn-primary",children:"Browse Properties"})]})]})}}},function(e){e.O(0,[971,117,744],function(){return e(e.s=9772)}),_N_E=e.O()}]);