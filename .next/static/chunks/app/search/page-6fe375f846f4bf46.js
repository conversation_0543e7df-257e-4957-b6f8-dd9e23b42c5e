(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[797],{7557:function(e,l,s){Promise.resolve().then(s.bind(s,104))},740:function(e,l,s){"use strict";s.d(l,{Z:function(){return i}});let i=(0,s(9763).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},2489:function(e,l,s){"use strict";s.d(l,{Z:function(){return i}});let i=(0,s(9763).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},104:function(e,l,s){"use strict";s.r(l),s.d(l,{default:function(){return u}});var i=s(7437),t=s(2265),n=s(2448),a=s(1900),r=s(740),c=s(2489);function o(e){let{onApply:l,onClose:s}=e,[n,a]=(0,t.useState)({}),o=(e,l)=>{a(s=>({...s,[e]:l||void 0}))};return(0,i.jsxs)("div",{className:"card",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(r.Z,{size:20,className:"text-zillow-blue"}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-zillow-dark-gray",children:"Advanced Filters"})]}),(0,i.jsx)("button",{onClick:s,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,i.jsx)(c.Z,{size:20})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-zillow-dark-gray mb-2",children:"Zoning Type"}),(0,i.jsxs)("select",{value:n.zoning||"",onChange:e=>o("zoning",e.target.value),className:"select-field",children:[(0,i.jsx)("option",{value:"",children:"All Zoning Types"}),(0,i.jsx)("option",{value:"Commercial",children:"Commercial"}),(0,i.jsx)("option",{value:"Residential",children:"Residential"}),(0,i.jsx)("option",{value:"Industrial",children:"Industrial"}),(0,i.jsx)("option",{value:"Mixed Use",children:"Mixed Use"}),(0,i.jsx)("option",{value:"Agricultural",children:"Agricultural"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-zillow-dark-gray mb-2",children:"Minimum Price"}),(0,i.jsxs)("select",{value:n.minPrice||"",onChange:e=>o("minPrice",parseInt(e.target.value)),className:"select-field",children:[(0,i.jsx)("option",{value:"",children:"No Minimum"}),(0,i.jsx)("option",{value:"25000",children:"$25,000"}),(0,i.jsx)("option",{value:"50000",children:"$50,000"}),(0,i.jsx)("option",{value:"75000",children:"$75,000"}),(0,i.jsx)("option",{value:"100000",children:"$100,000"}),(0,i.jsx)("option",{value:"150000",children:"$150,000"}),(0,i.jsx)("option",{value:"200000",children:"$200,000"}),(0,i.jsx)("option",{value:"300000",children:"$300,000"}),(0,i.jsx)("option",{value:"500000",children:"$500,000"}),(0,i.jsx)("option",{value:"750000",children:"$750,000"}),(0,i.jsx)("option",{value:"1000000",children:"$1,000,000"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-zillow-dark-gray mb-2",children:"Maximum Price"}),(0,i.jsxs)("select",{value:n.maxPrice||"",onChange:e=>o("maxPrice",parseInt(e.target.value)),className:"select-field",children:[(0,i.jsx)("option",{value:"",children:"No Maximum"}),(0,i.jsx)("option",{value:"50000",children:"$50,000"}),(0,i.jsx)("option",{value:"75000",children:"$75,000"}),(0,i.jsx)("option",{value:"100000",children:"$100,000"}),(0,i.jsx)("option",{value:"150000",children:"$150,000"}),(0,i.jsx)("option",{value:"200000",children:"$200,000"}),(0,i.jsx)("option",{value:"300000",children:"$300,000"}),(0,i.jsx)("option",{value:"500000",children:"$500,000"}),(0,i.jsx)("option",{value:"750000",children:"$750,000"}),(0,i.jsx)("option",{value:"1000000",children:"$1,000,000"}),(0,i.jsx)("option",{value:"2000000",children:"$2,000,000"}),(0,i.jsx)("option",{value:"5000000",children:"$5,000,000"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-zillow-dark-gray mb-2",children:"Chain Lease Potential"}),(0,i.jsxs)("select",{value:n.chainPotential||"",onChange:e=>o("chainPotential",e.target.value),className:"select-field",children:[(0,i.jsx)("option",{value:"",children:"All Potential Levels"}),(0,i.jsx)("option",{value:"Very High",children:"Very High"}),(0,i.jsx)("option",{value:"High",children:"High"}),(0,i.jsx)("option",{value:"Medium",children:"Medium"}),(0,i.jsx)("option",{value:"Low",children:"Low"}),(0,i.jsx)("option",{value:"None",children:"None"})]})]})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t border-zillow-border",children:[(0,i.jsx)("button",{onClick:()=>{a({})},className:"btn-secondary",children:"Reset Filters"}),(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("button",{onClick:s,className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),(0,i.jsx)("button",{onClick:()=>{l(n)},className:"btn-primary",children:"Apply Filters"})]})]})]})}var d=s(1837),x=s(9314);function u(){let[e,l]=(0,t.useState)([]),[s,r]=(0,t.useState)(!0),[c,u]=(0,t.useState)(!1),[h,m]=(0,t.useState)(""),[j,p]=(0,t.useState)({}),[v,g]=(0,t.useState)(!1),{showNotification:y}=(0,x.l)(),f=(0,t.useCallback)(async()=>{try{r(!0);let e=await d.G.searchProperties({query:"",filters:{}});l(e.results)}catch(e){console.error("Error loading properties:",e),y("Failed to load properties","error")}finally{r(!1)}},[y]);(0,t.useEffect)(()=>{f()},[f]);let N=async(e,s)=>{try{u(!0),m(e),p(s);let i=await d.G.searchProperties({query:e,filters:s});l(i.results),y("Found ".concat(i.total," properties"),"success")}catch(e){console.error("Error searching properties:",e),y("Search failed. Please try again.","error")}finally{u(!1)}},b=async()=>{try{let e=await d.G.syncProperties();y("Successfully synced ".concat(e.syncedCount," properties!"),"success"),await f()}catch(e){console.error("Error syncing data:",e),y("Failed to sync data. Please try again.","error")}};return s?(0,i.jsx)("div",{className:"p-8",children:(0,i.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,i.jsx)("div",{className:"loading-spinner"})})}):(0,i.jsxs)("div",{className:"p-8 space-y-8",children:[(0,i.jsx)("div",{className:"card",children:(0,i.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-zillow-dark-gray mb-2",children:"Vacant Land Search"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Find the perfect vacant land opportunities in Daytona Beach, FL"})]}),(0,i.jsx)("div",{className:"mt-4 lg:mt-0",children:(0,i.jsxs)("button",{onClick:()=>g(!v),className:"btn-secondary",children:[v?"Hide":"Show"," Advanced Filters"]})})]})}),(0,i.jsx)(n.y,{onSearch:N,onSyncData:b,isSearching:c}),v&&(0,i.jsx)(o,{onApply:e=>{N(h,{...j,...e}),g(!1)},onClose:()=>g(!1)}),(0,i.jsx)("div",{className:"card",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-xl font-semibold text-zillow-dark-gray",children:"Search Results"}),(0,i.jsxs)("p",{className:"text-gray-600",children:[e.length," properties found",h&&' for "'.concat(h,'"')]})]}),e.length>0&&(0,i.jsx)("div",{className:"text-sm text-gray-500",children:"Showing all results"})]})}),(0,i.jsx)(a.a,{properties:e,searchQuery:h,filters:j})]})}}},function(e){e.O(0,[415,474,971,117,744],function(){return e(e.s=7557)}),_N_E=e.O()}]);