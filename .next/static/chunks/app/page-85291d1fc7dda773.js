(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{2036:function(e,s,a){Promise.resolve().then(a.bind(a,2197))},2197:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return h}});var t=a(7437),l=a(2265),r=a(3448);function c(){let[e,s]=(0,l.useState)(new Date);return(0,l.useEffect)(()=>{let e=setInterval(()=>{s(new Date)},1e3);return()=>clearInterval(e)},[]),(0,t.jsx)("section",{className:"card",children:(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between",children:[(0,t.jsxs)("div",{className:"mb-4 lg:mb-0",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-zillow-dark-gray mb-2",children:"Hello, Admin!"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Welcome to your vacant land search dashboard"})]}),(0,t.jsxs)("div",{className:"text-right text-gray-600",children:[(0,t.jsx)("div",{className:"text-lg font-medium",children:(0,r.p6)(e)}),(0,t.jsx)("div",{className:"text-sm",children:(0,r.mr)(e)})]})]})})}function i(e){let{stats:s}=e,a=[{label:"Total Properties",value:(0,r.uf)(s.totalProperties),color:"text-zillow-blue"},{label:"Average Price",value:(0,r.T4)(s.averagePrice),color:"text-zillow-blue"},{label:"New Listings (24h)",value:(0,r.uf)(s.newListings),color:"text-zillow-blue"},{label:"Sold This Month",value:(0,r.uf)(s.soldThisMonth),color:"text-zillow-blue"}];return(0,t.jsx)("section",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:a.map((e,s)=>(0,t.jsxs)("div",{className:"stat-card",children:[(0,t.jsx)("div",{className:"text-3xl font-bold mb-2 ".concat(e.color),children:e.value}),(0,t.jsx)("div",{className:"text-gray-600 text-sm",children:e.label})]},s))})}var n=a(2448),o=a(1900),d=a(1837),u=a(9314);function h(){let[e,s]=(0,l.useState)(null),[a,r]=(0,l.useState)([]),[h,x]=(0,l.useState)(!0),[f,m]=(0,l.useState)(!1),[g,y]=(0,l.useState)(""),[v,b]=(0,l.useState)({}),{showNotification:j}=(0,u.l)(),p=(0,l.useCallback)(async()=>{try{x(!0);let[e,a]=await Promise.all([d.G.getDashboardStats(),d.G.searchProperties({query:"",filters:{}})]);s(e),r(a.results)}catch(e){console.error("Error loading dashboard data:",e),j("Failed to load dashboard data","error")}finally{x(!1)}},[j]);(0,l.useEffect)(()=>{p()},[p]);let N=async(e,s)=>{try{m(!0),y(e),b(s);let a=await d.G.searchProperties({query:e,filters:s});r(a.results)}catch(e){console.error("Error searching properties:",e),j("Search failed. Please try again.","error")}finally{m(!1)}},w=async()=>{try{let e=await d.G.syncProperties();j("Successfully synced ".concat(e.syncedCount," properties!"),"success"),await p()}catch(e){console.error("Error syncing data:",e),j("Failed to sync data. Please try again.","error")}};return h?(0,t.jsx)("div",{className:"p-8",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"loading-spinner"})})}):(0,t.jsxs)("div",{className:"p-8 space-y-8",children:[(0,t.jsx)(c,{}),e&&(0,t.jsx)(i,{stats:e}),(0,t.jsx)(n.y,{onSearch:N,onSyncData:w,isSearching:f}),(0,t.jsx)(o.a,{properties:a,searchQuery:g,filters:v})]})}}},function(e){e.O(0,[415,474,971,117,744],function(){return e(e.s=2036)}),_N_E=e.O()}]);