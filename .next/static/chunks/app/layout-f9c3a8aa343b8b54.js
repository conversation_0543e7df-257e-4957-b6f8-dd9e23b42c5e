(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{4105:function(e,t,i){Promise.resolve().then(i.t.bind(i,9974,23)),Promise.resolve().then(i.t.bind(i,2778,23)),Promise.resolve().then(i.bind(i,4114)),Promise.resolve().then(i.bind(i,9314))},4114:function(e,t,i){"use strict";i.d(t,{Navbar:function(){return m}});var n=i(7437),s=i(2265),a=i(7648),l=i(9376),r=i(6221),o=i(3247),c=i(8997),d=i(525),h=i(8736),u=i(8728),f=i(2489),b=i(8293);let x=[{href:"/",label:"Dashboard",icon:r.Z},{href:"/search",label:"Land Search",icon:o.Z},{href:"/watchlist",label:"Watch List",icon:c.Z},{href:"/analytics",label:"Market Analytics",icon:d.Z},{href:"/reports",label:"Reports",icon:h.Z},{href:"/settings",label:"Settings",icon:u.Z}];function m(){let e=(0,l.usePathname)(),[t,i]=(0,s.useState)(!1);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("button",{onClick:()=>i(!t),className:"lg:hidden fixed top-4 left-4 z-50 p-2 bg-zillow-blue text-white rounded-lg shadow-lg",children:t?(0,n.jsx)(f.Z,{size:24}):(0,n.jsx)(b.Z,{size:24})}),t&&(0,n.jsx)("div",{className:"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40",onClick:()=>i(!1)}),(0,n.jsxs)("nav",{className:"\n          fixed left-0 top-0 h-full w-80 bg-gradient-to-b from-zillow-blue to-zillow-blue-dark\n          text-white z-40 shadow-zillow-lg transition-transform duration-300 ease-in-out\n          ".concat(t?"translate-x-0":"-translate-x-full lg:translate-x-0","\n        "),children:[(0,n.jsxs)("div",{className:"p-8 border-b border-white/10",children:[(0,n.jsx)("div",{className:"text-2xl font-bold mb-2",children:"Vacant Land Search"}),(0,n.jsx)("div",{className:"text-sm opacity-80",children:"Daytona Beach, FL"})]}),(0,n.jsx)("div",{className:"py-4",children:x.map(t=>{let s=t.icon,l=e===t.href;return(0,n.jsxs)(a.default,{href:t.href,className:"navbar-item ".concat(l?"active":""),onClick:()=>i(!1),children:[(0,n.jsx)(s,{size:20,className:"mr-3"}),t.label]},t.href)})}),(0,n.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-6 border-t border-white/10",children:[(0,n.jsx)("div",{className:"text-xs opacity-60",children:"\xa9 2024 Vacant Land Search"}),(0,n.jsx)("div",{className:"text-xs opacity-60 mt-1",children:"Powered by Real Estate API"})]})]})]})}},9314:function(e,t,i){"use strict";i.d(t,{Providers:function(){return r},l:function(){return l}});var n=i(7437),s=i(2265);let a=(0,s.createContext)(void 0);function l(){let e=(0,s.useContext)(a);if(!e)throw Error("useNotification must be used within a NotificationProvider");return e}function r(e){let{children:t}=e,[i,l]=(0,s.useState)([]),r=(0,s.useCallback)(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info",i=Math.random().toString(36).substr(2,9),n={id:i,message:e,type:t};l(e=>[...e,n]),setTimeout(()=>{l(e=>e.filter(e=>e.id!==i))},5e3)},[]),o=(0,s.useCallback)(e=>{l(t=>t.filter(t=>t.id!==e))},[]);return(0,n.jsxs)(a.Provider,{value:{showNotification:r},children:[t,(0,n.jsx)("div",{className:"fixed top-5 right-5 z-50 space-y-2",children:i.map(e=>(0,n.jsx)("div",{className:"notification notification-".concat(e.type," animate-slide-in"),onClick:()=>o(e.id),children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{children:e.message}),(0,n.jsx)("button",{onClick:()=>o(e.id),className:"ml-4 text-white hover:text-gray-200",children:"\xd7"})]})},e.id))})]})}},2778:function(){}},function(e){e.O(0,[635,892,971,117,744],function(){return e(e.s=4105)}),_N_E=e.O()}]);