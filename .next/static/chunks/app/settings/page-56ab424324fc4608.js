(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[938],{3130:function(e,s,a){Promise.resolve().then(a.bind(a,1918))},9763:function(e,s,a){"use strict";a.d(s,{Z:function(){return i}});var t=a(2265),l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),i=(e,s)=>{let a=(0,t.forwardRef)((a,i)=>{let{color:n="currentColor",size:r=24,strokeWidth:d=2,absoluteStrokeWidth:x,className:m="",children:o,...h}=a;return(0,t.createElement)("svg",{ref:i,...l,width:r,height:r,stroke:n,strokeWidth:x?24*Number(d)/Number(r):d,className:["lucide","lucide-".concat(c(e)),m].join(" "),...h},[...s.map(e=>{let[s,a]=e;return(0,t.createElement)(s,a)}),...Array.isArray(o)?o:[o]])});return a.displayName="".concat(e),a}},1918:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return d}});var t=a(7437);a(2265);var l=a(9763);let c=(0,l.Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),i=(0,l.Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]),n=(0,l.Z)("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]]),r=(0,l.Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);function d(){return(0,t.jsxs)("div",{className:"p-8 space-y-8",children:[(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-zillow-dark-gray mb-2",children:"Settings"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage your account and application preferences"})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,t.jsx)(c,{size:20,className:"text-zillow-blue"}),(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Account Settings"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Display Name"}),(0,t.jsx)("input",{type:"text",defaultValue:"Admin User",className:"input-field"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,t.jsx)("input",{type:"email",defaultValue:"<EMAIL>",className:"input-field"})]}),(0,t.jsx)("button",{className:"btn-primary",children:"Save Changes"})]})]}),(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,t.jsx)(i,{size:20,className:"text-zillow-blue"}),(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Notifications"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:"New property alerts"}),(0,t.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:"Price change notifications"}),(0,t.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:"Weekly market reports"}),(0,t.jsx)("input",{type:"checkbox",className:"rounded"})]})]})]}),(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,t.jsx)(n,{size:20,className:"text-zillow-blue"}),(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"API Configuration"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Mapbox Token"}),(0,t.jsx)("input",{type:"password",defaultValue:"pk.eyJ1Ijoic2F2ZXVuZGVyY29udHJhY3QiLCJhIjoiY202anNoNWtvMDRpMjJqb2x1MWZ3ZGYxbCJ9.Bxl18yPka0BUSrGibMTbhg",className:"input-field"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Real Estate API Key"}),(0,t.jsx)("input",{type:"password",defaultValue:"AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914",className:"input-field"})]})]})]}),(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,t.jsx)(r,{size:20,className:"text-zillow-blue"}),(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Database Settings"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:"Auto-sync properties"}),(0,t.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:"Cache search results"}),(0,t.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,t.jsx)("button",{className:"btn-secondary",children:"Clear Cache"})]})]})]})]})}}},function(e){e.O(0,[971,117,744],function(){return e(e.s=3130)}),_N_E=e.O()}]);