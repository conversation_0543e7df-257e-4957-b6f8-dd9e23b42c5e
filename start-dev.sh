#!/bin/bash

# 🚀 Quick Development Startup Script
# Starts backend in production mode, frontend in development mode

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Configuration
BACKEND_PORT=8080
FRONTEND_PORT=3000

# Cleanup function
cleanup() {
    print_header "🧹 Stopping services..."
    
    # Kill processes on our ports
    if lsof -Pi :$BACKEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_status "Stopping backend on port $BACKEND_PORT"
        lsof -ti:$BACKEND_PORT | xargs kill -9 2>/dev/null || true
    fi
    
    if lsof -Pi :$FRONTEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_status "Stopping frontend on port $FRONTEND_PORT"
        lsof -ti:$FRONTEND_PORT | xargs kill -9 2>/dev/null || true
    fi
    
    print_success "Services stopped"
    exit 0
}

trap cleanup SIGINT SIGTERM EXIT

print_header "🏖️ Starting Development Environment"

# Kill any existing processes
if lsof -Pi :$BACKEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
    print_status "Killing existing backend process"
    lsof -ti:$BACKEND_PORT | xargs kill -9 2>/dev/null || true
    sleep 2
fi

if lsof -Pi :$FRONTEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
    print_status "Killing existing frontend process"
    lsof -ti:$FRONTEND_PORT | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# Start backend
print_status "Building and starting Go backend..."
export PORT=$BACKEND_PORT
export DATABASE_URL=${DATABASE_URL:-"postgresql://wonderland_owner:<EMAIL>/wonderland?sslmode=require"}

# Build and start backend
go mod tidy
go build -o propbolt
./propbolt &
BACKEND_PID=$!

# Wait for backend
print_status "Waiting for backend to start..."
for i in {1..20}; do
    if lsof -Pi :$BACKEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_success "Backend started (PID: $BACKEND_PID)"
        break
    fi
    sleep 1
done

# Start frontend in development mode
print_status "Starting Next.js frontend in development mode..."
npm run dev &
FRONTEND_PID=$!

# Wait for frontend
print_status "Waiting for frontend to start..."
for i in {1..30}; do
    if lsof -Pi :$FRONTEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_success "Frontend started (PID: $FRONTEND_PID)"
        break
    fi
    sleep 1
done

print_header "🎉 Development Environment Ready!"
echo ""
echo -e "${GREEN}🌐 URLs:${NC}"
echo -e "   Frontend: http://localhost:$FRONTEND_PORT"
echo -e "   Backend:  http://localhost:$BACKEND_PORT"
echo ""
echo -e "${YELLOW}Press Ctrl+C to stop both services${NC}"

# Monitor processes
while true; do
    if ! kill -0 $BACKEND_PID 2>/dev/null; then
        echo "Backend died"
        exit 1
    fi
    if ! kill -0 $FRONTEND_PID 2>/dev/null; then
        echo "Frontend died"
        exit 1
    fi
    sleep 5
done
