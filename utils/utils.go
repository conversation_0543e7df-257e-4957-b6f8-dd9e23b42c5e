package utils

import (
	"bytes"
	"net/url"
	"regexp"
	"strings"
	"sync"
	"math/rand"
	"time"
	"net/http"
)

var regexSpace = regexp.MustCompile(`[\s ]+`)

func RemoveSpace(value string) string {
	return regexSpace.ReplaceAllString(strings.TrimSpace(value), " ")
}

func RemoveSpaceByte(value []byte) []byte {
	return regexSpace.ReplaceAll(bytes.TrimSpace(value), []byte(" "))
}

func ParseProxy(urlToParse, userName, password string) (*url.URL, error) {
	urlToUse, err := url.Parse(urlToParse)
	if err != nil {
		return nil, err
	}
	urlToUse.User = url.UserPassword(userName, password)
	return urlToUse, nil
}

// ProxyRotator manages a pool of proxy URLs and rotates between them
type ProxyRotator struct {
	proxies []*url.URL
	current int
	mutex   sync.RWMutex
	rand    *rand.Rand
}

// NewProxyRotator creates a new proxy rotator with the given proxy URLs
func NewProxyRotator(proxyURLs []string) (*ProxyRotator, error) {
	var proxies []*url.URL

	for _, proxyURL := range proxyURLs {
		if proxyURL == "" {
			continue
		}

		parsed, err := url.Parse(proxyURL)
		if err != nil {
			return nil, err
		}
		proxies = append(proxies, parsed)
	}

	return &ProxyRotator{
		proxies: proxies,
		current: 0,
		rand:    rand.New(rand.NewSource(time.Now().UnixNano())),
	}, nil
}

// GetNext returns the next proxy URL in rotation
func (pr *ProxyRotator) GetNext() *url.URL {
	pr.mutex.Lock()
	defer pr.mutex.Unlock()

	if len(pr.proxies) == 0 {
		return nil
	}

	proxy := pr.proxies[pr.current]
	pr.current = (pr.current + 1) % len(pr.proxies)
	return proxy
}

// GetRandom returns a random proxy URL from the pool
func (pr *ProxyRotator) GetRandom() *url.URL {
	pr.mutex.RLock()
	defer pr.mutex.RUnlock()

	if len(pr.proxies) == 0 {
		return nil
	}

	index := pr.rand.Intn(len(pr.proxies))
	return pr.proxies[index]
}

// Count returns the number of available proxies
func (pr *ProxyRotator) Count() int {
	pr.mutex.RLock()
	defer pr.mutex.RUnlock()
	return len(pr.proxies)
}

// GetProxyWithRetry returns a proxy URL and provides a callback to try the next one on failure
func (pr *ProxyRotator) GetProxyWithRetry() (*url.URL, func() *url.URL) {
	firstProxy := pr.GetNext()

	retryFunc := func() *url.URL {
		return pr.GetNext()
	}

	return firstProxy, retryFunc
}

// UserAgentRotator manages rotation of user agent strings
type UserAgentRotator struct {
	userAgents []string
	current    int
	mutex      sync.RWMutex
	rand       *rand.Rand
}

// Common user agent strings that are less likely to be blocked
var defaultUserAgents = []string{
	"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
	"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
	"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
	"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
	"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
	"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
	"Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0",
}

// NewUserAgentRotator creates a new user agent rotator
func NewUserAgentRotator() *UserAgentRotator {
	return &UserAgentRotator{
		userAgents: defaultUserAgents,
		current:    0,
		rand:       rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// GetRandom returns a random user agent string
func (uar *UserAgentRotator) GetRandom() string {
	uar.mutex.RLock()
	defer uar.mutex.RUnlock()

	if len(uar.userAgents) == 0 {
		return defaultUserAgents[0]
	}

	index := uar.rand.Intn(len(uar.userAgents))
	return uar.userAgents[index]
}

// SetBrowserHeaders sets realistic browser headers to avoid detection
func SetBrowserHeaders(req *http.Request, userAgent string) {
	req.Header.Set("User-Agent", userAgent)
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.9")
	req.Header.Set("Accept-Encoding", "gzip, deflate, br")
	req.Header.Set("DNT", "1")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Upgrade-Insecure-Requests", "1")
	req.Header.Set("Sec-Fetch-Dest", "document")
	req.Header.Set("Sec-Fetch-Mode", "navigate")
	req.Header.Set("Sec-Fetch-Site", "none")
	req.Header.Set("Sec-Fetch-User", "?1")

	// Add some randomization to headers
	rand.Seed(time.Now().UnixNano())
	if rand.Float32() < 0.5 {
		req.Header.Set("Cache-Control", "no-cache")
	} else {
		req.Header.Set("Cache-Control", "max-age=0")
	}

	// Randomly add some optional headers
	if rand.Float32() < 0.3 {
		req.Header.Set("Pragma", "no-cache")
	}
}
